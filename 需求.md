# 设计需求

## 整体要求

一个gerrit客户端，带有AI总结功能。

### 功能
1. 启动时获取My changes（`attention:self`）并展示
2. 选择一个项目后，info栏展示其信息，包括他人的评分状态、分支等; Data栏显示其diff
3. 用户可以发出AI总结命令，此时diff将嵌入到提示词模板中，由AI进行总结，总结可能很耗时，需要实时更新AI输出。
4. 用户可以发出查询命令并提供查询字符串，查询并展示。如果用户不提供查询字符串，则弹出查询字符串创建对话框，用户可以点击按钮快速添加查询条件。
5. 用户可以选中一些提交，发出评审加一命令，批量对提交进行评审加一
6. Info栏需要展示Cherry-Pick状态，即相同的ChangeId在其他分支的评分及合入状态
   * 如果某个提交在其他分支已经合入/评审，左侧的选择栏应该用不同状态来让用户知道哪些提交是其他分支评审过的
7. 批量查询功能，用户发出对应命令后，从用户剪贴板读取链接列表并加载（可能为富文本形式，需要解析里面的所有超链接）

### 交互特性
几乎由键盘完成，窗口底部有一个输入框`lineEdit_command`，接收用户输入的命令，解析成任务进行执行，任务执行期间用户可以输入命令，使得多个任务同时执行。

### 任务设计
所有操作被拆分成任务，每个任务维护一定的状态，通过这种程序结构降低维护与测试的难度。

### 环境

编译方法：
```
build.bat
```

### 测试要求
搭建gerrit环境进行测试，gerrit程序在`tests/gerrit-3.12.2.war`
