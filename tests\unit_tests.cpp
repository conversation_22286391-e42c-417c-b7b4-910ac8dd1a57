#include <QtTest/QtTest>
#include <QApplication>
#include <QSignalSpy>
#include <QTimer>
#include <memory>

#include "../models.h"
#include "../taskmanager.h"
#include "../gerritclient.h"
#include "../task.hpp"

class UnitTests : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Models tests
    void testChangeInfoSerialization();
    void testQueryBuilder();
    void testGerritConfig();

    // Task system tests
    void testTaskCreation();
    void testLambdaTask();

    // TaskManager tests
    void testTaskManagerBasics();
    void testTaskManagerConcurrency();
    void testTaskManagerSignals();

    // TaskStatusDisplay tests
    void testTaskStatusDisplay();
    void testTaskStatusDisplayCache();

    // GerritClient tests
    void testGerritClientConfig();
    void testQueryBuilding();

private:
    QApplication* app_;
    std::unique_ptr<TaskManager> taskManager_;
    std::unique_ptr<TaskStatusDisplay> statusDisplay_;
};

void UnitTests::initTestCase()
{
    // 初始化测试环境
    int argc = 0;
    char* argv[] = {nullptr};
    app_ = new QApplication(argc, argv);
}

void UnitTests::cleanupTestCase()
{
    delete app_;
}

void UnitTests::init()
{
    // 每个测试前的初始化
    taskManager_ = std::make_unique<TaskManager>();
    statusDisplay_ = std::make_unique<TaskStatusDisplay>(taskManager_.get());
}

void UnitTests::cleanup()
{
    // 每个测试后的清理
    statusDisplay_.reset();
    taskManager_.reset();
}

void UnitTests::testChangeInfoSerialization()
{
    // 测试ChangeInfo的JSON序列化和反序列化
    ChangeInfo change;
    change.id = "test-id";
    change.number = 12345;
    change.subject = "Test change";
    change.status = "NEW";
    change.owner = "test-user";
    change.project = "test-project";
    change.branch = "main";
    change.created = QDateTime::currentDateTime();
    change.updated = QDateTime::currentDateTime();

    QJsonObject json = change.toJson();
    QCOMPARE(json["id"].toString(), change.id);
    QCOMPARE(json["_number"].toInt(), change.number);
    QCOMPARE(json["subject"].toString(), change.subject);

    ChangeInfo restored = ChangeInfo::fromJson(json);
    QCOMPARE(restored.id, change.id);
    QCOMPARE(restored.number, change.number);
    QCOMPARE(restored.subject, change.subject);
}

void UnitTests::testQueryBuilder()
{
    // 测试查询构建器
    QString myChanges = QueryBuilder::myChanges();
    QVERIFY(myChanges.contains("attention:self"));

    QString openChanges = QueryBuilder::openChanges();
    QVERIFY(openChanges.contains("status:open"));

    // 测试基本查询构建
    QVERIFY(!myChanges.isEmpty());
    QVERIFY(!openChanges.isEmpty());
}

void UnitTests::testGerritConfig()
{
    // 测试Gerrit配置
    GerritConfig config;
    config.serverUrl = "http://localhost:8080";
    config.username = "test-user";
    config.password = "test-pass";

    QJsonObject json = config.toJson();
    QCOMPARE(json["serverUrl"].toString(), config.serverUrl);
    QCOMPARE(json["username"].toString(), config.username);

    GerritConfig restored = GerritConfig::fromJson(json);
    QCOMPARE(restored.serverUrl, config.serverUrl);
    QCOMPARE(restored.username, config.username);
}

void UnitTests::testTaskCreation()
{
    // 测试任务创建
    auto task = testd::Task::create("Test Task", "Test Description");
    QVERIFY(task != nullptr);
    QCOMPARE(QString::fromStdString(task->getName()), QString("Test Task"));
    QCOMPARE(QString::fromStdString(task->getDescription()), QString("Test Description"));
    QCOMPARE(task->getState(), testd::Task::State::CREATE);
}



void UnitTests::testLambdaTask()
{
    // 测试Lambda任务
    int counter = 0;
    auto task = testd::LambdaTask::create("Counter Task", "Increment counter",
        [&counter](testd::LambdaTask*, testd::Task::State, const QJsonObject&) -> int {
            counter = 42;
            return 0;
        });

    QCOMPARE(counter, 0);

    // 直接调用executeCustom来测试lambda功能
    task->executeCustom();

    QCOMPARE(counter, 42);
}

void UnitTests::testTaskManagerBasics()
{
    // 测试TaskManager基本功能
    QVERIFY(taskManager_->getAllTasks().isEmpty());
    QCOMPARE(taskManager_->getRunningTasks().size(), 0);

    auto task = std::static_pointer_cast<testd::Task>(
        testd::LambdaTask::create("Test Task", "Basic test",
            [](testd::LambdaTask*, testd::Task::State, const QJsonObject&) -> int {
                return 0;
            }));
    QString taskId = taskManager_->addTask(task);

    QVERIFY(!taskId.isEmpty());
    QCOMPARE(taskManager_->getAllTasks().size(), 1);
    QVERIFY(taskManager_->getAllTasks().contains(taskId));

    auto retrievedTask = taskManager_->getTask(taskId);
    QVERIFY(retrievedTask != nullptr);
}

void UnitTests::testTaskManagerConcurrency()
{
    // 测试TaskManager并发控制
    taskManager_->setMaxConcurrentTasks(2);
    QCOMPARE(taskManager_->getMaxConcurrentTasks(), 2);

    // 添加多个任务
    QStringList taskIds;
    for (int i = 0; i < 3; ++i) {
        auto task = std::static_pointer_cast<testd::Task>(
            testd::LambdaTask::create(
                QString("Task %1").arg(i).toStdString(),
                "Concurrent test",
                [](testd::LambdaTask*, testd::Task::State, const QJsonObject&) -> int {
                    QThread::msleep(50); // 模拟工作
                    return 0;
                }
            ));
        taskIds << taskManager_->addTask(task);
    }

    QCOMPARE(taskIds.size(), 3);
    QCOMPARE(taskManager_->getAllTasks().size(), 3);

    // 检查基本功能
    QVERIFY(taskManager_->getRunningTasks().size() <= 2);
}

void UnitTests::testTaskManagerSignals()
{
    // 测试TaskManager信号
    QSignalSpy addedSpy(taskManager_.get(), &TaskManager::taskAdded);

    auto task = std::static_pointer_cast<testd::Task>(
        testd::LambdaTask::create("Signal Test", "Test signals",
            [](testd::LambdaTask*, testd::Task::State, const QJsonObject&) -> int {
                return 0;
            }));
    QString taskId = taskManager_->addTask(task);

    QCOMPARE(addedSpy.count(), 1);
    QCOMPARE(addedSpy.at(0).at(0).toString(), taskId);
}

void UnitTests::testTaskStatusDisplay()
{
    // 测试TaskStatusDisplay基本功能
    QVERIFY(statusDisplay_->isEnabled());
    QVERIFY(!statusDisplay_->isAutoRefresh());

    statusDisplay_->setEnabled(false);
    QVERIFY(!statusDisplay_->isEnabled());

    statusDisplay_->setAutoRefresh(true);
    QVERIFY(statusDisplay_->isAutoRefresh());

    statusDisplay_->setRefreshInterval(500);
    QCOMPARE(statusDisplay_->getRefreshInterval(), 500);
}

void UnitTests::testTaskStatusDisplayCache()
{
    // 测试TaskStatusDisplay缓存功能
    auto task = std::static_pointer_cast<testd::Task>(
        testd::LambdaTask::create("Cache Test", "Test cache",
            [](testd::LambdaTask*, testd::Task::State, const QJsonObject&) -> int {
                return 0;
            }));
    QString taskId = taskManager_->addTask(task);

    // 等待缓存更新
    QEventLoop loop;
    QTimer::singleShot(100, &loop, &QEventLoop::quit);
    loop.exec();

    auto displayInfo = statusDisplay_->getTaskDisplayInfo(taskId);
    QCOMPARE(displayInfo.id, taskId);
    QCOMPARE(displayInfo.name, QString("Cache Test"));
    QCOMPARE(displayInfo.description, QString("Test cache"));

    auto allInfo = statusDisplay_->getTaskDisplayInfo();
    QVERIFY(allInfo.size() >= 1);
}

void UnitTests::testGerritClientConfig()
{
    // 测试GerritClient配置
    GerritConfig config;
    config.serverUrl = "http://test.gerrit.com";
    config.username = "testuser";
    config.password = "testpass";

    GerritClient client;
    client.setConfig(config);

    auto retrievedConfig = client.getConfig();
    QCOMPARE(retrievedConfig.serverUrl, config.serverUrl);
    QCOMPARE(retrievedConfig.username, config.username);
}

void UnitTests::testQueryBuilding()
{
    // 测试查询构建
    QString query = "status:open project:test";
    QVERIFY(!query.isEmpty());
    QVERIFY(query.contains("status:open"));
    QVERIFY(query.contains("project:test"));

    // 测试复杂查询
    QString complexQuery = QueryBuilder::myChanges() + " AND " + QueryBuilder::openChanges();
    QVERIFY(complexQuery.contains("attention:self"));
    QVERIFY(complexQuery.contains("status:open"));
}

QTEST_MAIN(UnitTests)
#include "unit_tests.moc"
