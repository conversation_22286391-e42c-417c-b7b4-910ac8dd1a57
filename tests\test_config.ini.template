# Gerrit Client Qt Integration Test Configuration
# Copy this file to test_config.ini and fill in your actual values

[Gerrit]
# Gerrit server URL (required)
serverUrl=http://localhost:8080

# Username for authentication (required)
username=admin

# Password for authentication (optional for development mode)
password=

[TestRepo]
# Path to a test git repository (optional)
path=/tmp/gerrit_test_repo

# Remote name (default: origin)
remote=origin

# Branch to use for testing (default: main)
branch=main

[AI]
# AI service endpoint (optional)
endpoint=http://localhost:11434/api/generate

# AI model name (optional)
model=llama2

# API key (optional)
apiKey=

[Testing]
# Timeout for network operations (seconds)
networkTimeout=30

# Timeout for task operations (seconds)
taskTimeout=60

# Enable verbose logging
verbose=true
