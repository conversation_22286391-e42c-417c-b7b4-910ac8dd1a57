# Gerrit测试环境

本目录包含用于测试Gerrit客户端的完整测试环境。

## 文件说明

- `gerrit-3.12.2.war` - Gerrit服务器WAR包
- `manage-gerrit.sh` - 主管理脚本
- `start-gerrit.sh` - 启动Gerrit服务器
- `stop-gerrit.sh` - 停止Gerrit服务器
- `init-test-data.sh` - 初始化测试数据
- `gerrit-site/` - Gerrit站点目录（运行后生成）
- `test-repo/` - 测试Git仓库（初始化后生成）

## 快速开始

### 1. 启动Gerrit服务器

```bash
cd tests
./manage-gerrit.sh start
```

首次运行会自动初始化Gerrit站点，可能需要几分钟时间。

### 2. 初始化测试数据

```bash
./manage-gerrit.sh init
```

这会创建测试项目和一些示例提交。

### 3. 访问Gerrit界面

在浏览器中打开: http://localhost:8080

由于使用开发模式，你可以以任何用户身份登录（推荐使用"admin"）。

### 4. 配置客户端

在Gerrit客户端中配置：
- 服务器URL: `http://localhost:8080`
- 用户名: `admin`（或任何用户名）
- 密码: 留空

## 管理命令

使用 `./manage-gerrit.sh` 脚本管理测试环境：

```bash
# 查看帮助
./manage-gerrit.sh help

# 检查状态
./manage-gerrit.sh status

# 启动服务器
./manage-gerrit.sh start

# 停止服务器
./manage-gerrit.sh stop

# 重启服务器
./manage-gerrit.sh restart

# 初始化测试数据
./manage-gerrit.sh init

# 查看日志
./manage-gerrit.sh logs

# 清理所有数据
./manage-gerrit.sh clean
```

## 测试场景

初始化后，你可以测试以下功能：

### 1. 查询变更
```
# 在客户端命令框中输入
my                    # 查看我的变更
query status:open     # 查看开放的变更
query project:test-project  # 查看测试项目的变更
```

### 2. AI总结
1. 选择一个变更
2. 在命令框中输入 `ai` 或 `summary`
3. 查看AI标签页的总结结果

### 3. 批量评审
1. 选择多个变更（Ctrl+点击）
2. 在命令框中输入 `review 1` 进行+1评审

### 4. 批量查询
1. 复制包含Gerrit链接的文本到剪贴板
2. 在命令框中输入 `batch`

## 故障排除

### Gerrit无法启动
1. 检查Java版本（需要Java 11+）
2. 检查端口8080是否被占用
3. 查看日志：`./manage-gerrit.sh logs`

### 无法访问Gerrit界面
1. 确认Gerrit已启动：`./manage-gerrit.sh status`
2. 检查防火墙设置
3. 尝试重启：`./manage-gerrit.sh restart`

### 测试数据初始化失败
1. 确认Gerrit已运行
2. 检查Git配置
3. 查看详细错误信息

### 客户端连接失败
1. 确认服务器URL正确
2. 检查网络连接
3. 尝试在浏览器中访问Gerrit

## 开发模式说明

测试环境使用Gerrit的开发模式，具有以下特点：

- 无需真实的用户认证
- 可以以任何用户身份登录
- 适合开发和测试
- 不适合生产环境

## 清理环境

如果需要重新开始：

```bash
# 停止服务器并清理所有数据
./manage-gerrit.sh clean

# 重新启动并初始化
./manage-gerrit.sh start
./manage-gerrit.sh init
```

## 单元测试

### 运行单元测试

```bash
# 编译并运行所有测试
./run-tests.sh

# 只编译测试
./run-tests.sh --build

# 只运行测试（假设已编译）
./run-tests.sh --run

# 使用CTest运行
./run-tests.sh --ctest

# 清理构建文件
./run-tests.sh --clean
```

### 测试覆盖

单元测试覆盖以下模块：

1. **数据模型测试**
   - ChangeInfo序列化/反序列化
   - QueryBuilder查询构建
   - GerritConfig配置管理

2. **任务系统测试**
   - Task基础功能
   - LambdaTask执行
   - 任务状态管理

3. **TaskManager测试**
   - 任务添加/获取
   - 并发控制
   - 信号机制

4. **TaskStatusDisplay测试**
   - 状态显示
   - 缓存机制
   - 自动刷新

5. **GerritClient测试**
   - 配置管理
   - 查询构建

### 测试结果

所有14个测试用例都通过：
- testChangeInfoSerialization ✓
- testQueryBuilder ✓
- testGerritConfig ✓
- testTaskCreation ✓
- testLambdaTask ✓
- testTaskManagerBasics ✓
- testTaskManagerConcurrency ✓
- testTaskManagerSignals ✓
- testTaskStatusDisplay ✓
- testTaskStatusDisplayCache ✓
- testGerritClientConfig ✓
- testQueryBuilding ✓

## 注意事项

1. 测试环境仅用于开发和测试
2. 数据存储在本地，重启系统后数据仍然保留
3. 如果修改了Gerrit配置，需要重启服务器
4. 建议定期备份重要的测试数据
5. 单元测试使用offscreen平台，无需图形界面
