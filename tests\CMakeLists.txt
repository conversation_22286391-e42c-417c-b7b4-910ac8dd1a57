cmake_minimum_required(VERSION 3.5)

project(gerrit-client-tests)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt组件
find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Widgets Network Test)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Widgets Network Test)


# 启用Qt的MOC
set(CMAKE_AUTOMOC ON)

# 包含父目录的源文件
set(PARENT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/..)

# 源文件列表（不包括main.cpp）
set(SOURCES
    ${PARENT_DIR}/models.cpp
    ${PARENT_DIR}/gerritclient.cpp
    ${PARENT_DIR}/taskmanager.cpp
)

# 头文件列表
set(HEADERS
    ${PARENT_DIR}/models.h
    ${PARENT_DIR}/gerritclient.h
    ${PARENT_DIR}/taskmanager.h
    ${PARENT_DIR}/task.hpp
)

# 集成测试需要的额外源文件
set(INTEGRATION_SOURCES
    ${SOURCES}
    ${PARENT_DIR}/mainwindow.cpp
)

set(INTEGRATION_HEADERS
    ${HEADERS}
    ${PARENT_DIR}/mainwindow.h
)

# 创建测试可执行文件
add_executable(unit_tests
    unit_tests.cpp
    ${SOURCES}
    ${HEADERS}
)

# 链接库
target_link_libraries(unit_tests 
    PRIVATE 
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
    Qt${QT_VERSION_MAJOR}::Test
)

# 包含目录
target_include_directories(unit_tests PRIVATE ${PARENT_DIR})

# 启用测试
enable_testing()

# 创建集成测试可执行文件
add_executable(integration_tests
    integration_tests.cpp
    ${INTEGRATION_SOURCES}
    ${INTEGRATION_HEADERS}
)

# 链接库（集成测试）
target_link_libraries(integration_tests
    PRIVATE
    Qt${QT_VERSION_MAJOR}::Core
    Qt${QT_VERSION_MAJOR}::Widgets
    Qt${QT_VERSION_MAJOR}::Network
    Qt${QT_VERSION_MAJOR}::Test
)

# 包含目录（集成测试）
target_include_directories(integration_tests PRIVATE ${PARENT_DIR})

# 启用UIC（集成测试需要UI文件）
set_target_properties(integration_tests PROPERTIES
    AUTOUIC ON
)

# 添加测试
add_test(NAME UnitTests COMMAND unit_tests)
add_test(NAME IntegrationTests COMMAND integration_tests)

# 设置测试属性
set_tests_properties(UnitTests PROPERTIES
    TIMEOUT 30
    ENVIRONMENT "QT_QPA_PLATFORM=offscreen"
)

set_tests_properties(IntegrationTests PROPERTIES
    TIMEOUT 120
    ENVIRONMENT "QT_QPA_PLATFORM=offscreen"
)
