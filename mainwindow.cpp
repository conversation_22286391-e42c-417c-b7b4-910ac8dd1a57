#include "mainwindow.h"
#include "./ui_mainwindow.h"
#include <QApplication>
#include <QStandardItemModel>
#include <QHeaderView>
#include <QSplitter>
#include <memory>
#include "models.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , infoModel_(new QStandardItemModel(this))
    , settings_(new QSettings("GerritClient", "Settings", this))
{
    ui->setupUi(this);

    initializeUI();
    initializeClients();
    initializeConnections();
    loadSettings();

    // 确保窗口先显示，然后异步检查配置
    QTimer::singleShot(0, this, &MainWindow::checkAndPromptConfiguration);
}

MainWindow::~MainWindow()
{
    saveSettings();
    delete ui;
}

void MainWindow::initializeUI()
{
    // 设置窗口标题
    setWindowTitle("Gerrit Client with AI Summary");

    // 配置Info标签页的TreeView
    ui->treeView_info->setModel(infoModel_);
    ui->treeView_info->header()->setStretchLastSection(true);

    // 设置列表项可多选
    ui->listWidget_changes->setSelectionMode(QAbstractItemView::ExtendedSelection);

    // 设置命令输入框提示
    ui->lineEdit_command->setPlaceholderText("输入命令 (输入 help 查看帮助)");

    // 设置状态栏
    statusBar()->showMessage("就绪");
}

void MainWindow::initializeClients()
{
    // 初始化Gerrit客户端
    gerritClient_ = std::make_unique<GerritClient>(this);
    gerritConfig_ = GerritConfig::defaultConfig();
    gerritClient_->setConfig(gerritConfig_);

    // 初始化AI客户端
    aiClient_ = std::make_unique<AISummaryClient>(this);
    aiConfig_ = AISummaryConfig::defaultConfig();
    aiClient_->setConfig(aiConfig_);

    // 初始化任务管理器
    taskManager_ = std::make_unique<TaskManager>(this);
    taskDisplay_ = std::make_unique<TaskStatusDisplay>(taskManager_.get(), this);
}

void MainWindow::initializeConnections()
{
    // 命令输入框连接
    connect(ui->lineEdit_command, &QLineEdit::returnPressed,
            this, &MainWindow::onCommandEntered);

    // 变更列表选择连接
    connect(ui->listWidget_changes, &QListWidget::itemSelectionChanged,
            this, &MainWindow::onChangeSelectionChanged);

    // 菜单动作连接
    connect(ui->action_Gerrit, &QAction::triggered,
            this, &MainWindow::showGerritConfigDialog);
    connect(ui->action_AI, &QAction::triggered,
            this, &MainWindow::showAIConfigDialog);
    connect(ui->action_1, &QAction::triggered,
            [this]() { executeBatchReviewCommand(1); });

    // Gerrit客户端信号连接
    connect(gerritClient_.get(), &GerritClient::changesReceived,
            this, &MainWindow::onChangesReceived);
    connect(gerritClient_.get(), &GerritClient::changeDetailReceived,
            this, &MainWindow::onChangeDetailReceived);
    connect(gerritClient_.get(), &GerritClient::diffReceived,
            this, &MainWindow::onDiffReceived);
    connect(gerritClient_.get(), &GerritClient::relatedChangesReceived,
            this, &MainWindow::onRelatedChangesReceived);
    connect(gerritClient_.get(), &GerritClient::reviewSubmitted,
            this, &MainWindow::onReviewSubmitted);
    connect(gerritClient_.get(), &GerritClient::errorOccurred,
            this, &MainWindow::onGerritError);
    connect(gerritClient_.get(), &GerritClient::connectionTested,
            this, &MainWindow::onConnectionTested);

    // AI客户端信号连接
    connect(aiClient_.get(), &AISummaryClient::summaryReceived,
            this, &MainWindow::onSummaryReceived);
    connect(aiClient_.get(), &AISummaryClient::summaryProgress,
            this, &MainWindow::onSummaryProgress);
    connect(aiClient_.get(), &AISummaryClient::errorOccurred,
            this, &MainWindow::onAIError);

    // 任务管理器信号连接
    connect(taskManager_.get(), &TaskManager::taskAdded,
            this, &MainWindow::onTaskAdded);
    connect(taskManager_.get(), &TaskManager::taskFinished,
            this, &MainWindow::onTaskFinished);
    connect(taskManager_.get(), &TaskManager::taskError,
            this, &MainWindow::onTaskError);
}

void MainWindow::loadSettings()
{
    // 加载Gerrit配置
    settings_->beginGroup("Gerrit");
    gerritConfig_.serverUrl = settings_->value("serverUrl", gerritConfig_.serverUrl).toString();
    gerritConfig_.username = settings_->value("username", gerritConfig_.username).toString();
    gerritConfig_.password = settings_->value("password", gerritConfig_.password).toString();
    gerritConfig_.useHttps = settings_->value("useHttps", gerritConfig_.useHttps).toBool();
    gerritConfig_.timeout = settings_->value("timeout", gerritConfig_.timeout).toInt();
    settings_->endGroup();

    // 加载AI配置
    settings_->beginGroup("AI");
    aiConfig_.endpoint = settings_->value("endpoint", aiConfig_.endpoint).toString();
    aiConfig_.model = settings_->value("model", aiConfig_.model).toString();
    aiConfig_.apiKey = settings_->value("apiKey", aiConfig_.apiKey).toString();
    aiConfig_.promptTemplate = settings_->value("promptTemplate", aiConfig_.promptTemplate).toString();
    aiConfig_.maxTokens = settings_->value("maxTokens", aiConfig_.maxTokens).toInt();
    aiConfig_.temperature = settings_->value("temperature", aiConfig_.temperature).toDouble();
    settings_->endGroup();

    // 应用配置
    gerritClient_->setConfig(gerritConfig_);
    aiClient_->setConfig(aiConfig_);

    // 加载窗口状态
    restoreGeometry(settings_->value("geometry").toByteArray());
    restoreState(settings_->value("windowState").toByteArray());
}

void MainWindow::saveSettings()
{
    // 保存Gerrit配置
    settings_->beginGroup("Gerrit");
    settings_->setValue("serverUrl", gerritConfig_.serverUrl);
    settings_->setValue("username", gerritConfig_.username);
    settings_->setValue("password", gerritConfig_.password);
    settings_->setValue("useHttps", gerritConfig_.useHttps);
    settings_->setValue("timeout", gerritConfig_.timeout);
    settings_->endGroup();

    // 保存AI配置
    settings_->beginGroup("AI");
    settings_->setValue("endpoint", aiConfig_.endpoint);
    settings_->setValue("model", aiConfig_.model);
    settings_->setValue("apiKey", aiConfig_.apiKey);
    settings_->setValue("promptTemplate", aiConfig_.promptTemplate);
    settings_->setValue("maxTokens", aiConfig_.maxTokens);
    settings_->setValue("temperature", aiConfig_.temperature);
    settings_->endGroup();

    // 保存窗口状态
    settings_->setValue("geometry", saveGeometry());
    settings_->setValue("windowState", saveState());
}

void MainWindow::checkAndPromptConfiguration()
{
    // 检查Gerrit配置是否完整
    bool hasGerritConfig = !gerritConfig_.serverUrl.isEmpty() &&
                          !gerritConfig_.username.isEmpty();

    if (!hasGerritConfig) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this,
            "配置Gerrit服务器",
            "检测到Gerrit服务器尚未配置。\n\n是否现在配置？\n\n点击'Yes'配置服务器，点击'No'稍后手动配置。",
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::Yes
        );

        if (reply == QMessageBox::Yes) {
            showGerritConfigDialog();
        } else {
            showStatusMessage("请在菜单中配置Gerrit服务器后再使用相关功能");
        }
    } else {
        // 配置完整，自动加载My Changes
        showStatusMessage("正在加载My Changes...");
        executeQueryCommand(QueryBuilder::myChanges());
    }
}

void MainWindow::onCommandEntered()
{
    QString command = ui->lineEdit_command->text().trimmed();
    if (command.isEmpty()) {
        return;
    }

    ui->lineEdit_command->clear();
    parseCommand(command);
}

void MainWindow::parseCommand(const QString& command)
{
    QStringList parts = command.split(' ', Qt::SkipEmptyParts);
    if (parts.isEmpty()) {
        return;
    }

    QString cmd = parts[0].toLower();

    if (cmd == "help" || cmd == "h") {
        executeHelpCommand();
    } else if (cmd == "query" || cmd == "q") {
        QString query = parts.size() > 1 ? parts.mid(1).join(' ') : "";
        executeQueryCommand(query);
    } else if (cmd == "ai" || cmd == "summary") {
        executeAISummaryCommand();
    } else if (cmd == "review" || cmd == "r") {
        int score = parts.size() > 1 ? parts[1].toInt() : 1;
        executeBatchReviewCommand(score);
    } else if (cmd == "batch" || cmd == "b") {
        executeBatchQueryCommand();
    } else if (cmd == "reload") {
        executeQueryCommand(QueryBuilder::myChanges());
    } else if (cmd == "my") {
        executeQueryCommand(QueryBuilder::myChanges());
    } else if (cmd == "drafts") {
        executeQueryCommand(QueryBuilder::myDrafts());
    } else if (cmd == "reviews") {
        executeQueryCommand(QueryBuilder::myReviews());
    } else {
        showStatusMessage(QString("未知命令: %1 (输入 help 查看帮助)").arg(cmd));
    }
}

void MainWindow::executeQueryCommand(const QString& query)
{
    QString finalQuery = query;

    if (finalQuery.isEmpty()) {
        showQueryBuilderDialog();
        return;
    }

    showStatusMessage(QString("查询中: %1").arg(finalQuery));

    auto task = gerritClient_->queryChanges(finalQuery);
    QString taskId = taskManager_->addTask(task);
    taskManager_->startTask(taskId);
}

void MainWindow::executeAISummaryCommand()
{
    if (currentDiff_.isEmpty()) {
        showStatusMessage("请先选择一个变更以获取其diff");
        return;
    }

    showStatusMessage("正在生成AI总结...");
    ui->textBrowser_ai->clear();
    ui->textBrowser_ai->append("正在生成AI总结，请稍候...\n");

    auto task = aiClient_->summarizeDiff(currentDiff_);
    QString taskId = taskManager_->addTask(task);
    taskManager_->startTask(taskId);
}

void MainWindow::executeBatchReviewCommand(int score)
{
    QStringList changeIds = getSelectedChangeIds();
    if (changeIds.isEmpty()) {
        showStatusMessage("请先选择要评审的变更");
        return;
    }

    QString message = QString("批量评审 %1 个变更，评分: %2").arg(changeIds.size()).arg(score);
    showStatusMessage(message);

    auto task = gerritClient_->batchReview(changeIds, score, "Batch review");
    QString taskId = taskManager_->addTask(task);
    taskManager_->startTask(taskId);
}

void MainWindow::executeBatchQueryCommand()
{
    QList<QUrl> urls = extractUrlsFromClipboard();
    if (urls.isEmpty()) {
        showStatusMessage("剪贴板中没有找到有效的Gerrit链接");
        return;
    }

    QStringList changeIds;
    for (const QUrl& url : urls) {
        QString changeId = extractChangeIdFromUrl(url);
        if (!changeId.isEmpty()) {
            changeIds.append(changeId);
        }
    }

    if (changeIds.isEmpty()) {
        showStatusMessage("从剪贴板链接中没有提取到有效的Change ID");
        return;
    }

    showStatusMessage(QString("批量查询 %1 个变更").arg(changeIds.size()));

    // 创建批量查询任务
    auto batchTask = testd::Task::create(
        "Batch Query",
        QString("Batch querying %1 changes").arg(changeIds.size()).toStdString(),
        testd::Task::SubtaskRunMode::PARALLEL,
        false
    );

    for (const QString& changeId : changeIds) {
        auto detailTask = gerritClient_->getChangeDetail(changeId);
        batchTask->addSubTask(detailTask);
    }

    QString taskId = taskManager_->addTask(batchTask);
    taskManager_->startTask(taskId);
}

void MainWindow::executeHelpCommand()
{
    QString helpText = R"(
Gerrit Client 命令帮助:

基础命令:
  help, h              - 显示此帮助信息
  query <条件>, q      - 查询变更 (无条件时打开查询构建器)
  ai, summary          - 对当前选中变更生成AI总结
  review <分数>, r     - 批量评审选中的变更 (默认+1)
  batch, b             - 从剪贴板批量查询变更
  reload               - 重新加载My Changes

快捷查询:
  my                   - 我的变更 (attention:self)
  drafts               - 我的草稿
  reviews              - 我需要评审的变更

查询示例:
  query status:open project:myproject
  query owner:john branch:master
  query attention:self -owner:self

评审示例:
  review 1             - 给选中变更+1
  review -1            - 给选中变更-1
  review 2             - 给选中变更+2

使用说明:
1. 启动时自动加载My Changes
2. 选择变更后可在Info/Data标签页查看详情
3. 支持多选变更进行批量操作
4. AI总结需要先配置AI服务端点
)";

    QMessageBox::information(this, "命令帮助", helpText);
}

// 事件处理方法实现
void MainWindow::onChangeSelectionChanged()
{
    QList<QListWidgetItem*> selectedItems = ui->listWidget_changes->selectedItems();
    if (selectedItems.isEmpty()) {
        return;
    }

    // 获取第一个选中项的变更信息
    QListWidgetItem* item = selectedItems.first();
    int index = ui->listWidget_changes->row(item);

    if (index >= 0 && index < currentChanges_.size()) {
        const ChangeInfo& change = currentChanges_[index];
        currentChange_ = change;

        // 更新Info标签页
        updateChangeInfo(change);

        // 获取diff信息
        auto diffTask = gerritClient_->getChangeDiff(change.id);
        QString taskId = taskManager_->addTask(diffTask);
        taskManager_->startTask(taskId);

        // 获取相关变更信息
        auto relatedTask = gerritClient_->getRelatedChanges(change.id);
        taskId = taskManager_->addTask(relatedTask);
        taskManager_->startTask(taskId);
    }
}

void MainWindow::onChangesReceived(const QList<ChangeInfo>& changes)
{
    currentChanges_ = changes;
    updateChangesList(changes);
    showStatusMessage(QString("加载了 %1 个变更").arg(changes.size()));
}

void MainWindow::onChangeDetailReceived(const ChangeInfo& change)
{
    // 更新当前变更信息
    for (int i = 0; i < currentChanges_.size(); ++i) {
        if (currentChanges_[i].id == change.id) {
            currentChanges_[i] = change;
            break;
        }
    }

    // 如果是当前选中的变更，更新显示
    if (currentChange_.id == change.id) {
        currentChange_ = change;
        updateChangeInfo(change);
    }
}

void MainWindow::onDiffReceived(const QString& changeId, const QString& diff)
{
    if (currentChange_.id == changeId) {
        currentDiff_ = diff;
        updateChangeDiff(diff);
    }
}

void MainWindow::onRelatedChangesReceived(const QString& changeId, const QList<ChangeInfo>& related)
{
    Q_UNUSED(changeId)
    Q_UNUSED(related)
    // TODO: 实现Cherry-Pick状态显示
}

void MainWindow::onReviewSubmitted(const QString& changeId, bool success)
{
    if (success) {
        showStatusMessage(QString("评审提交成功: %1").arg(changeId));
    } else {
        showStatusMessage(QString("评审提交失败: %1").arg(changeId));
    }
}

void MainWindow::onGerritError(const QString& error)
{
    showStatusMessage(QString("Gerrit错误: %1").arg(error));
}

void MainWindow::onConnectionTested(bool success, const QString& message)
{
    if (success) {
        showStatusMessage("Gerrit连接测试成功");
    } else {
        showStatusMessage(QString("Gerrit连接测试失败: %1").arg(message));
    }
}

void MainWindow::onSummaryReceived(const QString& summary)
{
    updateAISummary(summary, false);
    showStatusMessage("AI总结生成完成");
}

void MainWindow::onSummaryProgress(const QString& partialSummary)
{
    updateAISummary(partialSummary, true);
}

void MainWindow::onAIError(const QString& error)
{
    updateAISummary(QString("AI总结生成失败: %1").arg(error), false);
    showStatusMessage(QString("AI错误: %1").arg(error));
}

void MainWindow::onTaskAdded(const QString& taskId, const QString& name)
{
    Q_UNUSED(taskId)
    showStatusMessage(QString("任务已添加: %1").arg(name));
}

void MainWindow::onTaskFinished(const QString& taskId, bool success)
{
    Q_UNUSED(taskId)
    if (success) {
        showStatusMessage("任务完成");
    } else {
        showStatusMessage("任务失败");
    }
}

void MainWindow::onTaskError(const QString& taskId, const QString& error)
{
    QString errorMsg = error.isEmpty() ? "未知错误" : error;
    showStatusMessage(QString("任务错误 [%1]: %2").arg(taskId.left(8)).arg(errorMsg));
    qWarning() << "Task error:" << taskId << errorMsg;
}

// UI更新方法实现
void MainWindow::updateChangesList(const QList<ChangeInfo>& changes)
{
    ui->listWidget_changes->clear();

    for (const ChangeInfo& change : changes) {
        QString displayText = formatChangeForDisplay(change);
        QListWidgetItem* item = new QListWidgetItem(displayText);

        // 根据状态设置颜色
        if (change.status == "MERGED") {
            item->setForeground(QColor(0, 128, 0)); // 绿色
        } else if (change.status == "ABANDONED") {
            item->setForeground(QColor(128, 128, 128)); // 灰色
        } else if (change.hasReviewed) {
            item->setForeground(QColor(0, 0, 255)); // 蓝色
        }

        ui->listWidget_changes->addItem(item);
    }
}

void MainWindow::updateChangeInfo(const ChangeInfo& change)
{
    infoModel_->clear();
    infoModel_->setHorizontalHeaderLabels({"属性", "值"});

    // 基本信息
    infoModel_->appendRow({new QStandardItem("Change ID"), new QStandardItem(change.changeId)});
    infoModel_->appendRow({new QStandardItem("项目"), new QStandardItem(change.project)});
    infoModel_->appendRow({new QStandardItem("分支"), new QStandardItem(change.branch)});
    infoModel_->appendRow({new QStandardItem("主题"), new QStandardItem(change.subject)});
    infoModel_->appendRow({new QStandardItem("状态"), new QStandardItem(change.status)});
    infoModel_->appendRow({new QStandardItem("提交者"), new QStandardItem(change.owner)});
    infoModel_->appendRow({new QStandardItem("创建时间"), new QStandardItem(change.created.toString())});
    infoModel_->appendRow({new QStandardItem("更新时间"), new QStandardItem(change.updated.toString())});

    // 标签信息
    if (!change.labels.isEmpty()) {
        QString labelText = formatLabelInfo(change.labels);
        infoModel_->appendRow({new QStandardItem("评分"), new QStandardItem(labelText)});
    }

    // 可合并状态
    infoModel_->appendRow({new QStandardItem("可合并"), new QStandardItem(change.mergeable ? "是" : "否")});

    ui->treeView_info->expandAll();
}

void MainWindow::updateChangeDiff(const QString& diff)
{
    ui->textBrowser_data->setPlainText(diff);
}

void MainWindow::updateAISummary(const QString& summary, bool append)
{
    if (append) {
        ui->textBrowser_ai->insertPlainText(summary);
    } else {
        ui->textBrowser_ai->setPlainText(summary);
    }

    // 滚动到底部
    QTextCursor cursor = ui->textBrowser_ai->textCursor();
    cursor.movePosition(QTextCursor::End);
    ui->textBrowser_ai->setTextCursor(cursor);
}

void MainWindow::showStatusMessage(const QString& message, int timeout)
{
    statusBar()->showMessage(message, timeout);
}

// 辅助方法实现
QStringList MainWindow::getSelectedChangeIds() const
{
    QStringList changeIds;
    QList<QListWidgetItem*> selectedItems = ui->listWidget_changes->selectedItems();

    for (QListWidgetItem* item : selectedItems) {
        int index = ui->listWidget_changes->row(item);
        if (index >= 0 && index < currentChanges_.size()) {
            changeIds.append(currentChanges_[index].id);
        }
    }

    return changeIds;
}

QString MainWindow::formatChangeForDisplay(const ChangeInfo& change) const
{
    return QString("[%1] %2 - %3 (%4)")
           .arg(change.number)
           .arg(change.subject)
           .arg(change.owner)
           .arg(change.status);
}

QString MainWindow::formatLabelInfo(const QJsonObject& labels) const
{
    QStringList labelTexts;

    for (auto it = labels.begin(); it != labels.end(); ++it) {
        QString labelName = it.key();
        QJsonObject labelObj = it.value().toObject();

        if (labelObj.contains("value")) {
            int value = labelObj["value"].toInt();
            if (value != 0) {
                QString sign = value > 0 ? "+" : "";
                labelTexts.append(QString("%1: %2%3").arg(labelName, sign).arg(value));
            }
        }
    }

    return labelTexts.join(", ");
}

QList<QUrl> MainWindow::extractUrlsFromClipboard() const
{
    QList<QUrl> urls;
    QClipboard* clipboard = QApplication::clipboard();
    QString text = clipboard->text();

    // 使用正则表达式提取URL
    QRegularExpression urlRegex(R"(https?://[^\s<>"{}|\\^`\[\]]+)");
    QRegularExpressionMatchIterator matches = urlRegex.globalMatch(text);

    while (matches.hasNext()) {
        QRegularExpressionMatch match = matches.next();
        QUrl url(match.captured(0));
        if (url.isValid()) {
            urls.append(url);
        }
    }

    return urls;
}

QString MainWindow::extractChangeIdFromUrl(const QUrl& url) const
{
    QString path = url.path();

    // Gerrit URL格式: /c/project/+/12345 或 /c/12345
    QRegularExpression changeRegex(R"(/c/(?:[^/]+/\+/)?(\d+))");
    QRegularExpressionMatch match = changeRegex.match(path);

    if (match.hasMatch()) {
        return match.captured(1);
    }

    return QString();
}

// 配置对话框实现
void MainWindow::showGerritConfigDialog()
{
    bool ok;
    QString serverUrl = QInputDialog::getText(this, "Gerrit配置",
                                            "服务器URL:", QLineEdit::Normal,
                                            gerritConfig_.serverUrl, &ok);
    if (!ok) return;

    QString username = QInputDialog::getText(this, "Gerrit配置",
                                           "用户名:", QLineEdit::Normal,
                                           gerritConfig_.username, &ok);
    if (!ok) return;

    QString password = QInputDialog::getText(this, "Gerrit配置",
                                           "密码/Token:", QLineEdit::Password,
                                           gerritConfig_.password, &ok);
    if (!ok) return;

    gerritConfig_.serverUrl = serverUrl;
    gerritConfig_.username = username;
    gerritConfig_.password = password;

    gerritClient_->setConfig(gerritConfig_);
    saveSettings();

    showStatusMessage("Gerrit配置已更新");

    // 测试连接
    auto testTask = gerritClient_->testConnection();
    QString taskId = taskManager_->addTask(testTask);
    taskManager_->startTask(taskId);
}

void MainWindow::showAIConfigDialog()
{
    bool ok;
    QString endpoint = QInputDialog::getText(this, "AI配置",
                                           "AI服务端点:", QLineEdit::Normal,
                                           aiConfig_.endpoint, &ok);
    if (!ok) return;

    QString model = QInputDialog::getText(this, "AI配置",
                                        "模型名称:", QLineEdit::Normal,
                                        aiConfig_.model, &ok);
    if (!ok) return;

    aiConfig_.endpoint = endpoint;
    aiConfig_.model = model;

    aiClient_->setConfig(aiConfig_);
    saveSettings();

    showStatusMessage("AI配置已更新");
}

void MainWindow::showQueryBuilderDialog()
{
    QStringList items;
    items << "attention:self" << "owner:self" << "reviewer:self -owner:self"
          << "status:open" << "status:merged" << "is:draft";

    bool ok;
    QString item = QInputDialog::getItem(this, "查询构建器",
                                       "选择查询条件:", items, 0, true, &ok);
    if (ok && !item.isEmpty()) {
        executeQueryCommand(item);
    }
}

void MainWindow::onMenuActionTriggered()
{
    // 菜单动作处理 - 这个方法在连接中没有实际使用，可以留空或移除
}
