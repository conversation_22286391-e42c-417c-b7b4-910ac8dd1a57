#!/bin/bash

# Gerrit Client Qt 测试运行脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="$PROJECT_ROOT/build/tests"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

echo "=== Gerrit Client Qt 测试套件 ==="
echo ""

# 检查依赖
check_dependencies() {
    echo "检查依赖..."
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        echo "错误: 未找到CMake"
        exit 1
    fi
    
    # 检查Qt
    if ! pkg-config --exists Qt5Core 2>/dev/null && ! pkg-config --exists Qt6Core 2>/dev/null; then
        echo "警告: 未找到Qt开发包，可能会编译失败"
    fi
    
    echo "✓ 依赖检查完成"
}

# 编译测试
build_tests() {
    echo ""
    echo "编译测试..."
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # 配置CMake
    echo "配置CMake..."
    if ! cmake ..; then
        echo "错误: CMake配置失败"
        exit 1
    fi
    
    # 编译
    echo "编译测试程序..."
    if ! make -j$(nproc); then
        echo "错误: 编译失败"
        exit 1
    fi
    
    echo "✓ 编译完成"
}

# 运行测试
run_tests() {
    echo ""
    echo "运行测试..."
    
    cd "$BUILD_DIR"
    
    # 设置环境变量
    export QT_QPA_PLATFORM=offscreen
    export QT_LOGGING_RULES="*.debug=false"
    
    # 运行测试
    if [ -f "./unit_tests" ]; then
        echo "执行单元测试..."
        ./unit_tests
        TEST_RESULT=$?
        
        if [ $TEST_RESULT -eq 0 ]; then
            echo ""
            echo "✓ 所有测试通过！"
        else
            echo ""
            echo "✗ 测试失败，退出码: $TEST_RESULT"
            exit $TEST_RESULT
        fi
    else
        echo "错误: 未找到测试可执行文件"
        exit 1
    fi
}

# 运行CTest
run_ctest() {
    echo ""
    echo "运行CTest..."
    
    cd "$BUILD_DIR"
    
    if ! ctest --output-on-failure; then
        echo "CTest执行失败"
        exit 1
    fi
    
    echo "✓ CTest完成"
}

# 清理
cleanup() {
    if [ "$1" = "--clean" ]; then
        echo ""
        echo "清理构建文件..."
        rm -rf "$BUILD_DIR"
        echo "✓ 清理完成"
    fi
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --clean     清理构建文件"
    echo "  --build     只编译，不运行测试"
    echo "  --run       只运行测试（假设已编译）"
    echo "  --ctest     使用CTest运行测试"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 编译并运行测试"
    echo "  $0 --clean      # 清理构建文件"
    echo "  $0 --build      # 只编译测试"
    echo "  $0 --ctest      # 使用CTest运行"
}

# 主逻辑
case "$1" in
    --clean)
        cleanup --clean
        ;;
    --build)
        check_dependencies
        build_tests
        ;;
    --run)
        run_tests
        ;;
    --ctest)
        check_dependencies
        build_tests
        run_ctest
        ;;
    --help|-h)
        show_help
        ;;
    "")
        check_dependencies
        build_tests
        run_tests
        ;;
    *)
        echo "错误: 未知选项 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac

echo ""
echo "测试完成！"
