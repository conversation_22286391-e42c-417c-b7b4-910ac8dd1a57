@echo off
setlocal

:: 1. 设置Qt环境变量
call "C:\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\qtenv2.bat" || (
    echo Error: Failed to set Qt environment
    exit /b 1
)

:: 2. 设置项目路径
set PROJECT_DIR=D:\git\gerrit-client-qt
set BUILD_DIR=D:\git\build-gerrit-client-Desktop_Qt_5_14_2_MinGW_64_bit-Debug

:: 3. 确保构建目录存在
if not exist "%BUILD_DIR%" mkdir "%BUILD_DIR%"

:: 4. 执行qmake
pushd "%BUILD_DIR%"
echo Running qmake...
"C:\Qt\Qt5.14.2\5.14.2\mingw73_64\bin\qmake.exe" "%PROJECT_DIR%\gerrit-client.pro" -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug" || (
    echo Error: qmake failed
    popd
    exit /b 1
)

:: 5. 执行make
echo Running make...
"C:\Qt\Qt5.14.2\Tools\mingw730_64\bin\mingw32-make.exe" || (
    echo Error: make failed
    popd
    exit /b 1
)

popd
echo Build successful!
endlocal
