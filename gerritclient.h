#ifndef GERRITCLIENT_H
#define GERRITCLIENT_H

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonObject>
#include <QJsonArray>
#include <QTimer>
#include <memory>
#include "models.h"
#include "task.hpp"

/**
 * @brief Gerrit REST API客户端
 */
class GerritClient : public QObject {
    Q_OBJECT

public:
    explicit GerritClient(QObject* parent = nullptr);
    ~GerritClient();

    // 配置管理
    void setConfig(const GerritConfig& config);
    GerritConfig getConfig() const;

    // 查询操作
    std::shared_ptr<testd::Task> queryChanges(const QString& query, int limit = 25);
    std::shared_ptr<testd::Task> getChangeDetail(const QString& changeId);
    std::shared_ptr<testd::Task> getChangeDiff(const QString& changeId);
    
    // Cherry-Pick状态查询
    std::shared_ptr<testd::Task> getRelatedChanges(const QString& changeId);
    
    // 评审操作
    std::shared_ptr<testd::Task> reviewChange(const QString& changeId, int score, const QString& message = "");
    std::shared_ptr<testd::Task> batchReview(const QStringList& changeIds, int score, const QString& message = "");
    
    // 连接测试
    std::shared_ptr<testd::Task> testConnection();

signals:
    void changesReceived(const QList<ChangeInfo>& changes);
    void changeDetailReceived(const ChangeInfo& change);
    void diffReceived(const QString& changeId, const QString& diff);
    void relatedChangesReceived(const QString& changeId, const QList<ChangeInfo>& related);
    void reviewSubmitted(const QString& changeId, bool success);
    void errorOccurred(const QString& error);
    void connectionTested(bool success, const QString& message);

private slots:
    void handleNetworkReply();
    void handleNetworkError(QNetworkReply::NetworkError error);

private:
    // 网络请求相关
    QNetworkAccessManager* networkManager_;
    GerritConfig config_;
    
    // 请求构建
    QNetworkRequest buildRequest(const QString& path) const;
    QString buildUrl(const QString& path) const;
    QByteArray buildAuthHeader() const;
    
    // 响应解析
    QJsonObject parseGerritResponse(const QByteArray& data) const;
    QJsonArray parseGerritArrayResponse(const QByteArray& data) const;
    
    // 任务创建辅助方法
    std::shared_ptr<testd::LambdaTask> createNetworkTask(
        const QString& name,
        const QString& description,
        const QString& path,
        const QString& method = "GET",
        const QJsonObject& payload = QJsonObject()
    );
    
    // 错误处理
    void handleApiError(const QJsonObject& response);
    QString formatNetworkError(QNetworkReply::NetworkError error) const;
};

/**
 * @brief AI总结客户端
 */
class AISummaryClient : public QObject {
    Q_OBJECT

public:
    explicit AISummaryClient(QObject* parent = nullptr);
    ~AISummaryClient();

    // 配置管理
    void setConfig(const AISummaryConfig& config);
    AISummaryConfig getConfig() const;

    // AI总结操作
    std::shared_ptr<testd::Task> summarizeDiff(const QString& diff);
    std::shared_ptr<testd::Task> summarizeChange(const ChangeInfo& change);

signals:
    void summaryReceived(const QString& summary);
    void summaryProgress(const QString& partialSummary);
    void errorOccurred(const QString& error);

private slots:
    void handleNetworkReply();
    void handleNetworkError(QNetworkReply::NetworkError error);

private:
    QNetworkAccessManager* networkManager_;
    AISummaryConfig config_;
    
    // 请求构建
    QNetworkRequest buildRequest() const;
    QJsonObject buildPayload(const QString& prompt) const;
    QString formatPrompt(const QString& diff) const;
    
    // 响应解析
    void parseStreamingResponse(const QByteArray& data);
    
    // 任务创建
    std::shared_ptr<testd::LambdaTask> createSummaryTask(
        const QString& name,
        const QString& description,
        const QString& prompt
    );
};

#endif // GERRITCLIENT_H
