#ifndef TASKMANAGER_H
#define TASKMANAGER_H

#include <QObject>
#include <QTimer>
#include <QQueue>
#include <QHash>
#include <QMutex>
#include <memory>
#include <QDateTime>
#include "task.hpp"

/**
 * @brief 任务管理器 - 管理任务的执行和状态
 */
class TaskManager : public QObject {
    Q_OBJECT

public:
    explicit TaskManager(QObject* parent = nullptr);
    ~TaskManager();

    // 任务管理
    QString addTask(std::shared_ptr<testd::Task> task);
    bool removeTask(const QString& taskId);
    std::shared_ptr<testd::Task> getTask(const QString& taskId) const;
    QStringList getRunningTasks() const;
    QStringList getAllTasks() const;
    
    // 任务控制
    void startTask(const QString& taskId);
    void stopTask(const QString& taskId);
    void stopAllTasks();
    
    // 任务状态查询
    testd::Task::State getTaskState(const QString& taskId) const;
    QString getTaskResult(const QString& taskId) const;
    int getTaskProgress(const QString& taskId) const;
    
    // 配置
    void setMaxConcurrentTasks(int max);
    int getMaxConcurrentTasks() const;
    void setTaskTimeout(int seconds);
    int getTaskTimeout() const;

signals:
    void taskAdded(const QString& taskId, const QString& name);
    void taskStarted(const QString& taskId);
    void taskFinished(const QString& taskId, bool success);
    void taskStateChanged(const QString& taskId, testd::Task::State state);
    void taskProgressUpdated(const QString& taskId, const QString& message);
    void taskError(const QString& taskId, const QString& error);
    void allTasksFinished();

public slots:
    void processTaskQueue();
    void cleanupFinishedTasks();

private slots:
    void onTaskStateChanged(testd::Task::State state);
    void onTaskProgressUpdated(const QString& message);
    void onTaskFinished(bool success);
    void onTaskError(const QString& error);

private:
    // 任务存储
    QHash<QString, std::shared_ptr<testd::Task>> tasks_;
    QQueue<QString> taskQueue_;
    QStringList runningTasks_;
    
    // 配置
    int maxConcurrentTasks_;
    int taskTimeout_;
    
    // 定时器
    QTimer* processTimer_;
    QTimer* cleanupTimer_;
    
    // 线程安全
    mutable QMutex tasksMutex_;
    
    // 辅助方法
    QString generateTaskId() const;
    void connectTaskSignals(std::shared_ptr<testd::Task> task, const QString& taskId);
    void disconnectTaskSignals(std::shared_ptr<testd::Task> task);
    bool canStartNewTask() const;
    void startNextTask();
    void updateTaskProgress(const QString& taskId);
};

/**
 * @brief 任务状态显示器 - 用于GUI显示任务状态
 */
class TaskStatusDisplay : public QObject {
    Q_OBJECT

public:
    explicit TaskStatusDisplay(TaskManager* taskManager, QObject* parent = nullptr);
    ~TaskStatusDisplay();

    // 显示控制
    void setEnabled(bool enabled);
    bool isEnabled() const;
    void setAutoRefresh(bool autoRefresh);
    bool isAutoRefresh() const;
    void setRefreshInterval(int milliseconds);
    int getRefreshInterval() const;

    // 任务信息获取
    struct TaskDisplayInfo {
        QString id;
        QString name;
        QString description;
        testd::Task::State state;
        QString stateText;
        QString progressMessage;
        int progressPercent;
        QDateTime startTime;
        QDateTime endTime;
        QString duration;
        bool hasSubTasks;
        int subTaskCount;
        int completedSubTasks;
    };
    
    QList<TaskDisplayInfo> getTaskDisplayInfo() const;
    TaskDisplayInfo getTaskDisplayInfo(const QString& taskId) const;

signals:
    void displayUpdated();
    void taskDisplayInfoChanged(const QString& taskId, const TaskDisplayInfo& info);

public slots:
    void refresh();
    void clear();

private slots:
    void onTaskAdded(const QString& taskId, const QString& name);
    void onTaskStateChanged(const QString& taskId, testd::Task::State state);
    void onTaskProgressUpdated(const QString& taskId, const QString& message);
    void onAutoRefresh();

private:
    TaskManager* taskManager_;
    bool enabled_;
    bool autoRefresh_;
    QTimer* refreshTimer_;
    
    // 缓存的显示信息
    QHash<QString, TaskDisplayInfo> displayCache_;
    mutable QMutex cacheMutex_;
    
    // 辅助方法
    QString formatDuration(const QDateTime& start, const QDateTime& end) const;
    QString formatState(testd::Task::State state) const;
    int calculateProgress(std::shared_ptr<testd::Task> task) const;
    void updateDisplayCache(const QString& taskId);
};

#endif // TASKMANAGER_H
