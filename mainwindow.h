#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QListWidgetItem>
#include <QTreeWidgetItem>
#include <QStandardItemModel>
#include <QClipboard>
#include <QRegularExpression>
#include <QInputDialog>
#include <QMessageBox>
#include <QSettings>
#include <memory>

#include "models.h"
#include "gerritclient.h"
#include "taskmanager.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // UI事件处理
    void onCommandEntered();
    void onChangeSelectionChanged();
    void onMenuActionTriggered();

    // Gerrit客户端事件
    void onChangesReceived(const QList<ChangeInfo>& changes);
    void onChangeDetailReceived(const ChangeInfo& change);
    void onDiffReceived(const QString& changeId, const QString& diff);
    void onRelatedChangesReceived(const QString& changeId, const QList<ChangeInfo>& related);
    void onReviewSubmitted(const QString& changeId, bool success);
    void onGerritError(const QString& error);
    void onConnectionTested(bool success, const QString& message);

    // AI总结事件
    void onSummaryReceived(const QString& summary);
    void onSummaryProgress(const QString& partialSummary);
    void onAIError(const QString& error);

    // 任务管理事件
    void onTaskAdded(const QString& taskId, const QString& name);
    void onTaskFinished(const QString& taskId, bool success);
    void onTaskError(const QString& taskId, const QString& error);

private:
    // UI组件
    Ui::MainWindow *ui;
    QStandardItemModel* infoModel_;

    // 核心组件
    std::unique_ptr<GerritClient> gerritClient_;
    std::unique_ptr<AISummaryClient> aiClient_;
    std::unique_ptr<TaskManager> taskManager_;
    std::unique_ptr<TaskStatusDisplay> taskDisplay_;

    // 数据存储
    QList<ChangeInfo> currentChanges_;
    ChangeInfo currentChange_;
    QString currentDiff_;

    // 配置
    GerritConfig gerritConfig_;
    AISummaryConfig aiConfig_;
    QSettings* settings_;

    // 初始化方法
    void initializeUI();
    void initializeClients();
    void initializeConnections();
    void loadSettings();
    void saveSettings();
    void checkAndPromptConfiguration();

    // 命令解析
    void parseCommand(const QString& command);
    void executeQueryCommand(const QString& query = "");
    void executeAISummaryCommand();
    void executeBatchReviewCommand(int score);
    void executeBatchQueryCommand();
    void executeHelpCommand();

    // UI更新方法
    void updateChangesList(const QList<ChangeInfo>& changes);
    void updateChangeInfo(const ChangeInfo& change);
    void updateChangeDiff(const QString& diff);
    void updateAISummary(const QString& summary, bool append = false);
    void showStatusMessage(const QString& message, int timeout = 3000);

    // 辅助方法
    QStringList getSelectedChangeIds() const;
    QString formatChangeForDisplay(const ChangeInfo& change) const;
    QString formatLabelInfo(const QJsonObject& labels) const;
    QList<QUrl> extractUrlsFromClipboard() const;
    QString extractChangeIdFromUrl(const QUrl& url) const;

    // 配置对话框
    void showGerritConfigDialog();
    void showAIConfigDialog();
    void showQueryBuilderDialog();
};

#endif // MAINWINDOW_H
