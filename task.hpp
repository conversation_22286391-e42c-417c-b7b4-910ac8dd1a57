/**
 * @file task.hpp
 * @brief 任务类头文件
 */

#ifndef TESTD_TASK_HPP
#define TESTD_TASK_HPP

#include <memory>
#include <string>
#include <list>
#include <cerrno>
#include <iostream>
#include <functional>
#include <vector>
#include <chrono>
#include <QObject>
#include <QVariant>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTimer>
#include <QString>

namespace testd {

/**
 * @brief 任务类
 */
class Task : public QObject, public std::enable_shared_from_this<Task> {
    Q_OBJECT
protected:
    struct Private { explicit Private() = default; };
public:
    enum class State {
        CREATE,
        INIT,
        RUNNING,
        FINISHED,
        ERROR
    };
    enum class SubtaskRunMode {
        SEQUENTIAL,
        ISOLATED,
        PARALLEL
    };

    Task(const std::string& name, const std::string& description, SubtaskRunMode subtaskRunMode, bool skipFailedTasks, Private, QObject* parent = nullptr)
        : QObject(parent), name(name), description(description), state(State::CREATE), createTime(std::chrono::steady_clock::now()), timeout(std::chrono::seconds(5)), subtaskRunMode(subtaskRunMode), skipFailedTasks(skipFailedTasks)
    {

    }
    static inline std::shared_ptr<Task> create(const std::string& name, const std::string& description = "", SubtaskRunMode subtaskRunMode = SubtaskRunMode::PARALLEL, bool skipFailedTasks = false)
    {
        return std::make_shared<Task>(name, description, subtaskRunMode, skipFailedTasks, Private());
    }
    std::shared_ptr<Task> getInstance()
    {
        return shared_from_this();
    }
    virtual ~Task()
    {}

    // 添加子任务
    std::shared_ptr<Task> addSubTask(const std::shared_ptr<Task>& task)
    {
        subTasks.push_back(task);
        return task;
    }

    // 禁止拷贝和赋值
    Task(const Task&) = delete;
    Task& operator=(const Task&) = delete;

    // Getters
    const std::string& getName() const { return name; }
    const std::string& getDescription() const { return description; }
    State getState() const { return state; }
    const std::string& getResultString() const { return resultString; }
    const std::list<std::shared_ptr<Task>>& getSubTasks() const { return subTasks; }
    std::chrono::steady_clock::time_point getCreateTime() const { return createTime; }
    std::chrono::steady_clock::time_point getStartTime() const { return startTime; }
    std::chrono::steady_clock::time_point getEndTime() const { return endTime; }
    std::chrono::steady_clock::duration getTimeout() const { return timeout; }
    SubtaskRunMode getSubtaskRunMode() const { return subtaskRunMode; }
    bool getSkipFailedTasks() const { return skipFailedTasks; }
    int getErrorCode() const { return errorCode; }

    // Setters
    void setName(const std::string& n) { name = n; }
    void setDescription(const std::string& d) { description = d; }
    void setState(State s) {
        if (state >= s){
            state = s;
            emit stateChanged(state);
            return;
        }
        State oldState = state;
        switch (state) {
        case State::CREATE:
            state = State::INIT;
            initTime = std::chrono::steady_clock::now();
            if (state == s) break;
            [[fallthrough]];
        case State::INIT:
            state = State::RUNNING;
            startTime = std::chrono::steady_clock::now();
            if (state == s) break;
            [[fallthrough]];
        case State::RUNNING:
            state = State::FINISHED;
            endTime = std::chrono::steady_clock::now();
            if (state == s) break;
            [[fallthrough]];
        case State::FINISHED:
            state = State::ERROR;
            endTime = std::chrono::steady_clock::now();
            if (state == s) break;
            [[fallthrough]];
        case State::ERROR:
            break;
        }
        if (oldState != state) {
            emit stateChanged(state);
            if (state == State::FINISHED) {
                emit finished(true);
            } else if (state == State::ERROR) {
                emit finished(false);
                emit errorOccurred(QString::fromStdString(resultString));
            }
        }
    }
    void setResultString(const std::string& s) { resultString = s; }
    void setTimeout(std::chrono::steady_clock::duration t) { timeout = t; }
    void setSubtaskRunMode(SubtaskRunMode m) { subtaskRunMode = m; }
    void setSkipFailedTasks(bool b) { skipFailedTasks = b; }

    // 执行任务
    virtual int execute(bool cleanupFlag = false) {
        if (state == State::FINISHED) {
            return 0;
        }else if (state == State::ERROR) {
            return -EPERM;
        }

        bool allFinished = true;
        for (auto& task : subTasks) {
            if (!task) {
                continue;
            }
            if (task->state == State::FINISHED) {
                continue;
            }
            int errorCode = task->execute(cleanupFlag);
            if (errorCode < 0 && errorCode != -EAGAIN) {
                setState(State::ERROR);
                return -EPERM;
            }
            bool taskFinished = task->state == State::FINISHED;
            if (skipFailedTasks) taskFinished |= task->state == State::ERROR;
            if (subtaskRunMode == SubtaskRunMode::PARALLEL || subtaskRunMode == SubtaskRunMode::SEQUENTIAL) {
                if (state == State::INIT || state == State::CREATE) taskFinished |= task->state == State::RUNNING;
            }
            if ((subtaskRunMode == SubtaskRunMode::ISOLATED || subtaskRunMode == SubtaskRunMode::SEQUENTIAL) && !taskFinished) {
                return -EAGAIN;
            }
            allFinished = allFinished && taskFinished;
        }
        if (!allFinished) {
            return -EAGAIN;
        }
        if (state == State::CREATE) {
            setState(State::INIT);
        }

        if (state == State::INIT) {
            int ret = executeCustom();
            if (ret < 0) {
                if (ret != -EAGAIN) {
                    std::cerr << "Task " << name << " init failed: " << ret << std::endl;
                    startTime = endTime = std::chrono::steady_clock::now();
                    state = State::ERROR;
                } else if (timeout.count() > 0) {
                    auto now = std::chrono::steady_clock::now();
                    if (now - initTime > timeout) {
                        std::cerr << "Task " << name << " init timeout." << std::endl;
                        startTime = endTime = std::chrono::steady_clock::now();
                        state = State::ERROR;
                        return -ETIMEDOUT;
                    }
                }
                return ret;
            }
            setState(State::RUNNING);
            return 0;
        }else if(state == State::RUNNING){
            int ret = executeCustom();
            if (ret < 0) {
                if (ret != -EAGAIN) {
                    std::cerr << "Task " << name << " running failed: " << ret << std::endl;
                    setState(State::ERROR);
                } else if (timeout.count() > 0) {
                    auto now = std::chrono::steady_clock::now();
                    if (now - startTime > timeout) {
                        std::cerr << "Task " << name << " running timeout." << std::endl;
                        setState(State::ERROR);
                        return -ETIMEDOUT;
                    }
                }
                return ret;
            }
            if (cleanupFlag) {
                for (auto it = subTasks.begin(); it != subTasks.end();) {
                    if ((*it)->getState() == State::FINISHED) {
                        (*it)->cleanup();
                        it = subTasks.erase(it);
                    } else {
                        ++it;
                    }
                }
            }
            setState(State::FINISHED);
            return 0;
        }
        return 0;
    }
    virtual int cleanup() { return 0; }
    virtual int executeCustom() {return 0; }

signals:
    void stateChanged(State newState);
    void progressUpdated(const QString& message);
    void finished(bool success);
    void errorOccurred(const QString& error);

public slots:
    void start() {
        if (state == State::CREATE) {
            setState(State::INIT);
            QTimer::singleShot(0, this, [this]() {
                execute();
            });
        }
    }

    void stop() {
        if (state == State::RUNNING || state == State::INIT) {
            setState(State::ERROR);
            setResultString("Task stopped by user");
        }
    }

protected:
    std::string name;           /**< 任务名称 */
    std::string description;    /**< 任务描述 */
    std::list<std::shared_ptr<Task>> subTasks; /**< 子任务列表 */
    State state;                /**< 任务状态 */
    int errorCode;              /**< 错误码 */
    std::string resultString;        /**< 任务结果 */
    std::chrono::steady_clock::time_point createTime;
    std::chrono::steady_clock::time_point initTime;
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::chrono::steady_clock::duration timeout;

    SubtaskRunMode subtaskRunMode;
    bool skipFailedTasks;

};

/**
 * @brief Lambda任务类 - 使用Qt信号槽机制
 */
class LambdaTask : public Task {
    Q_OBJECT

public:
    using ExecuteFunction = std::function<int(LambdaTask*, Task::State, const QJsonObject&)>;

    LambdaTask(const std::string& name, const std::string& description,
               ExecuteFunction executeFunc, SubtaskRunMode subtaskRunMode, bool skipFailedTasks,
               const QJsonObject& jsonData, Private, QObject* parent = nullptr)
        : Task(name, description, subtaskRunMode, skipFailedTasks, Private(), parent),
          executeFunc_(executeFunc), jsonData_(jsonData) {

        // 从JSON数据中设置超时
        if (jsonData_.contains("timeout")) {
            setTimeout(std::chrono::milliseconds(jsonData_["timeout"].toInt()));
        }

        // 从JSON数据中设置名称
        if (jsonData_.contains("name")) {
            setDescription(jsonData_["name"].toString().toStdString());
        }
    }

    ~LambdaTask() = default;

    static std::shared_ptr<LambdaTask> create(const std::string& name,
                                             const std::string& description,
                                             ExecuteFunction executeFunc,
                                             SubtaskRunMode subtaskRunMode = SubtaskRunMode::PARALLEL,
                                             bool skipFailedTasks = false,
                                             const QJsonObject& jsonData = QJsonObject()) {
        return std::make_shared<LambdaTask>(name, description, executeFunc, subtaskRunMode, skipFailedTasks, jsonData, Private());
    }

    virtual int executeCustom() override {
        if (executeFunc_) {
            return executeFunc_(this, getState(), jsonData_);
        }
        return 0;
    }

    const QJsonObject& getJsonData() const { return jsonData_; }
    void setJsonData(const QJsonObject& data) { jsonData_ = data; }

private:
    ExecuteFunction executeFunc_;
    QJsonObject jsonData_;
};


} // namespace testd

#endif // TESTD_TASK_HPP
