#!/bin/bash

# Gerrit测试环境管理脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GERRIT_SITE="$SCRIPT_DIR/gerrit-site"
GERRIT_URL="http://localhost:8080"

show_help() {
    echo "Gerrit测试环境管理脚本"
    echo ""
    echo "用法: $0 <命令>"
    echo ""
    echo "命令:"
    echo "  start     - 启动Gerrit服务器"
    echo "  stop      - 停止Gerrit服务器"
    echo "  restart   - 重启Gerrit服务器"
    echo "  status    - 检查Gerrit服务器状态"
    echo "  init      - 初始化测试数据"
    echo "  clean     - 清理Gerrit站点数据"
    echo "  logs      - 查看Gerrit日志"
    echo "  help      - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start     # 启动Gerrit"
    echo "  $0 init      # 初始化测试数据"
    echo "  $0 status    # 检查状态"
    echo "  $0 stop      # 停止Gerrit"
}

check_status() {
    if curl -s "$GERRIT_URL" > /dev/null; then
        echo "✓ Gerrit服务器正在运行"
        echo "  URL: $GERRIT_URL"
        
        # 检查版本信息
        VERSION=$(curl -s "$GERRIT_URL/config/server/version" 2>/dev/null)
        if [ -n "$VERSION" ]; then
            echo "  版本: $VERSION"
        fi
        
        return 0
    else
        echo "✗ Gerrit服务器未运行"
        return 1
    fi
}

start_gerrit() {
    echo "启动Gerrit服务器..."
    if check_status > /dev/null 2>&1; then
        echo "Gerrit已在运行"
        return 0
    fi
    
    "$SCRIPT_DIR/start-gerrit.sh"
}

stop_gerrit() {
    echo "停止Gerrit服务器..."
    "$SCRIPT_DIR/stop-gerrit.sh"
}

restart_gerrit() {
    echo "重启Gerrit服务器..."
    stop_gerrit
    sleep 2
    start_gerrit
}

init_test_data() {
    echo "初始化测试数据..."
    if ! check_status > /dev/null 2>&1; then
        echo "Gerrit未运行，正在启动..."
        start_gerrit
        sleep 5
    fi
    
    "$SCRIPT_DIR/init-test-data.sh"
}

clean_gerrit() {
    echo "清理Gerrit站点数据..."
    
    if check_status > /dev/null 2>&1; then
        echo "请先停止Gerrit服务器"
        read -p "是否现在停止? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            stop_gerrit
            sleep 2
        else
            echo "取消清理操作"
            return 1
        fi
    fi
    
    if [ -d "$GERRIT_SITE" ]; then
        echo "警告: 这将删除所有Gerrit数据，包括项目、用户、评审等"
        read -p "确定要继续吗? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$GERRIT_SITE"
            rm -rf "$SCRIPT_DIR/test-repo"
            echo "Gerrit站点数据已清理"
        else
            echo "取消清理操作"
        fi
    else
        echo "Gerrit站点目录不存在"
    fi
}

show_logs() {
    if [ -f "$GERRIT_SITE/logs/error_log" ]; then
        echo "=== Gerrit错误日志 ==="
        tail -n 50 "$GERRIT_SITE/logs/error_log"
    else
        echo "未找到错误日志文件"
    fi
    
    echo ""
    
    if [ -f "$GERRIT_SITE/logs/gerrit.log" ]; then
        echo "=== Gerrit主日志 ==="
        tail -n 50 "$GERRIT_SITE/logs/gerrit.log"
    else
        echo "未找到主日志文件"
    fi
}

# 主逻辑
case "$1" in
    start)
        start_gerrit
        ;;
    stop)
        stop_gerrit
        ;;
    restart)
        restart_gerrit
        ;;
    status)
        check_status
        ;;
    init)
        init_test_data
        ;;
    clean)
        clean_gerrit
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        echo "错误: 请指定命令"
        echo ""
        show_help
        exit 1
        ;;
    *)
        echo "错误: 未知命令 '$1'"
        echo ""
        show_help
        exit 1
        ;;
esac
