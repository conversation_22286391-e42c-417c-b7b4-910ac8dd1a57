#include "taskmanager.h"
#include <QUuid>
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>

TaskManager::TaskManager(QObject* parent)
    : QObject(parent)
    , maxConcurrentTasks_(3)
    , taskTimeout_(300) // 5分钟默认超时
    , processTimer_(new QTimer(this))
    , cleanupTimer_(new QTimer(this))
{
    // 设置定时器
    processTimer_->setInterval(100); // 100ms检查一次
    connect(processTimer_, &QTimer::timeout, this, &TaskManager::processTaskQueue);
    processTimer_->start();
    
    cleanupTimer_->setInterval(60000); // 1分钟清理一次
    connect(cleanupTimer_, &QTimer::timeout, this, &TaskManager::cleanupFinishedTasks);
    cleanupTimer_->start();
}

TaskManager::~TaskManager() {
    stopAllTasks();
}

QString TaskManager::addTask(std::shared_ptr<testd::Task> task) {
    if (!task) {
        return QString();
    }

    QString taskId;
    QString taskName;

    {
        QMutexLocker locker(&tasksMutex_);

        taskId = generateTaskId();
        tasks_[taskId] = task;
        taskQueue_.enqueue(taskId);
        taskName = QString::fromStdString(task->getName());

        // 连接信号
        connectTaskSignals(task, taskId);
    }

    // 在锁外发出信号，避免死锁
    emit taskAdded(taskId, taskName);

    return taskId;
}

bool TaskManager::removeTask(const QString& taskId) {
    QMutexLocker locker(&tasksMutex_);
    
    auto it = tasks_.find(taskId);
    if (it == tasks_.end()) {
        return false;
    }
    
    auto task = it.value();
    
    // 如果任务正在运行，先停止它
    if (task->getState() == testd::Task::State::RUNNING) {
        task->stop();
    }
    
    // 断开信号连接
    disconnectTaskSignals(task);
    
    // 从各个列表中移除
    tasks_.erase(it);
    runningTasks_.removeAll(taskId);
    
    // 从队列中移除（如果还在队列中）
    QQueue<QString> newQueue;
    while (!taskQueue_.isEmpty()) {
        QString id = taskQueue_.dequeue();
        if (id != taskId) {
            newQueue.enqueue(id);
        }
    }
    taskQueue_ = newQueue;
    
    return true;
}

std::shared_ptr<testd::Task> TaskManager::getTask(const QString& taskId) const {
    QMutexLocker locker(&tasksMutex_);
    return tasks_.value(taskId);
}

QStringList TaskManager::getRunningTasks() const {
    QMutexLocker locker(&tasksMutex_);
    return runningTasks_;
}

QStringList TaskManager::getAllTasks() const {
    QMutexLocker locker(&tasksMutex_);
    return tasks_.keys();
}

void TaskManager::startTask(const QString& taskId) {
    std::shared_ptr<testd::Task> task;
    bool shouldStart = false;
    bool shouldQueue = false;

    {
        QMutexLocker locker(&tasksMutex_);

        task = tasks_.value(taskId);
        if (!task) {
            return;
        }

        if (task->getState() == testd::Task::State::CREATE) {
            if (canStartNewTask()) {
                runningTasks_.append(taskId);
                shouldStart = true;
            } else {
                // 添加到队列等待
                if (!taskQueue_.contains(taskId)) {
                    taskQueue_.enqueue(taskId);
                    shouldQueue = true;
                }
            }
        }
    }

    // 在锁外启动任务和发出信号
    if (shouldStart && task) {
        task->start();
        emit taskStarted(taskId);
    }
}

void TaskManager::stopTask(const QString& taskId) {
    QMutexLocker locker(&tasksMutex_);
    
    auto task = tasks_.value(taskId);
    if (task) {
        task->stop();
        runningTasks_.removeAll(taskId);
    }
}

void TaskManager::stopAllTasks() {
    QMutexLocker locker(&tasksMutex_);
    
    for (auto& task : tasks_) {
        if (task->getState() == testd::Task::State::RUNNING ||
            task->getState() == testd::Task::State::INIT) {
            task->stop();
        }
    }
    
    runningTasks_.clear();
    taskQueue_.clear();
}

testd::Task::State TaskManager::getTaskState(const QString& taskId) const {
    QMutexLocker locker(&tasksMutex_);
    
    auto task = tasks_.value(taskId);
    if (task) {
        return task->getState();
    }
    
    return testd::Task::State::ERROR;
}

QString TaskManager::getTaskResult(const QString& taskId) const {
    QMutexLocker locker(&tasksMutex_);
    
    auto task = tasks_.value(taskId);
    if (task) {
        return QString::fromStdString(task->getResultString());
    }
    
    return QString();
}

int TaskManager::getTaskProgress(const QString& taskId) const {
    QMutexLocker locker(&tasksMutex_);
    
    auto task = tasks_.value(taskId);
    if (!task) {
        return 0;
    }
    
    // 简单的进度计算：基于状态和子任务完成情况
    switch (task->getState()) {
    case testd::Task::State::CREATE:
        return 0;
    case testd::Task::State::INIT:
        return 10;
    case testd::Task::State::RUNNING: {
        // 基于子任务完成情况计算进度
        const auto& subTasks = task->getSubTasks();
        if (subTasks.empty()) {
            return 50; // 没有子任务时返回50%
        }
        
        int completed = 0;
        for (const auto& subTask : subTasks) {
            if (subTask->getState() == testd::Task::State::FINISHED) {
                completed++;
            }
        }
        
        return 10 + (80 * completed / subTasks.size());
    }
    case testd::Task::State::FINISHED:
        return 100;
    case testd::Task::State::ERROR:
        return -1;
    }
    
    return 0;
}

void TaskManager::setMaxConcurrentTasks(int max) {
    maxConcurrentTasks_ = qMax(1, max);
}

int TaskManager::getMaxConcurrentTasks() const {
    return maxConcurrentTasks_;
}

void TaskManager::setTaskTimeout(int seconds) {
    taskTimeout_ = qMax(10, seconds);
}

int TaskManager::getTaskTimeout() const {
    return taskTimeout_;
}

void TaskManager::processTaskQueue() {
    QList<QPair<QString, std::shared_ptr<testd::Task>>> tasksToStart;
    QList<QPair<QString, std::shared_ptr<testd::Task>>> tasksToStop;

    {
        QMutexLocker locker(&tasksMutex_);

        // 收集要启动的任务
        while (!taskQueue_.isEmpty() && canStartNewTask()) {
            QString taskId = taskQueue_.dequeue();
            auto task = tasks_.value(taskId);

            if (task && task->getState() == testd::Task::State::CREATE) {
                runningTasks_.append(taskId);
                tasksToStart.append(qMakePair(taskId, task));
            }
        }

        // 检查超时的任务
        auto now = QDateTime::currentDateTime();
        for (const QString& taskId : runningTasks_) {
            auto task = tasks_.value(taskId);
            if (task) {
                auto taskStartTime = task->getStartTime();
                // 检查任务是否已经开始（startTime不为0）
                if (taskStartTime.time_since_epoch().count() > 0) {
                    auto startTime = QDateTime::fromMSecsSinceEpoch(
                        std::chrono::duration_cast<std::chrono::milliseconds>(
                            taskStartTime.time_since_epoch()).count());

                    if (startTime.secsTo(now) > taskTimeout_) {
                        tasksToStop.append(qMakePair(taskId, task));
                    }
                }
            }
        }
    }

    // 在锁外启动任务
    for (const auto& pair : tasksToStart) {
        pair.second->start();
        emit taskStarted(pair.first);
    }

    // 在锁外停止超时任务
    for (const auto& pair : tasksToStop) {
        qWarning() << "Task" << pair.first << "timed out, stopping";
        pair.second->stop();
    }
}

void TaskManager::cleanupFinishedTasks() {
    QMutexLocker locker(&tasksMutex_);
    
    QStringList toRemove;
    auto cutoffTime = QDateTime::currentDateTime().addSecs(-3600); // 1小时前
    
    for (auto it = tasks_.begin(); it != tasks_.end(); ++it) {
        auto task = it.value();
        if (task->getState() == testd::Task::State::FINISHED ||
            task->getState() == testd::Task::State::ERROR) {
            
            auto endTime = QDateTime::fromMSecsSinceEpoch(
                std::chrono::duration_cast<std::chrono::milliseconds>(
                    task->getEndTime().time_since_epoch()).count());
            
            if (endTime < cutoffTime) {
                toRemove.append(it.key());
            }
        }
    }
    
    // 移除过期的已完成任务
    for (const QString& taskId : toRemove) {
        auto task = tasks_.value(taskId);
        disconnectTaskSignals(task);
        tasks_.remove(taskId);
    }
    
    if (!toRemove.isEmpty()) {
        qDebug() << "Cleaned up" << toRemove.size() << "finished tasks";
    }
}

void TaskManager::onTaskStateChanged(testd::Task::State state) {
    testd::Task* task = qobject_cast<testd::Task*>(sender());
    if (!task) return;

    // 查找任务ID
    QString taskId;
    bool shouldEmitFinished = false;
    bool shouldEmitAllFinished = false;

    {
        QMutexLocker locker(&tasksMutex_);
        for (auto it = tasks_.begin(); it != tasks_.end(); ++it) {
            if (it.value().get() == task) {
                taskId = it.key();
                break;
            }
        }

        if (!taskId.isEmpty()) {
            if (state == testd::Task::State::FINISHED || state == testd::Task::State::ERROR) {
                runningTasks_.removeAll(taskId);
                shouldEmitFinished = true;

                if (runningTasks_.isEmpty() && taskQueue_.isEmpty()) {
                    shouldEmitAllFinished = true;
                }
            }
        }
    }

    // 在锁外发出信号
    if (!taskId.isEmpty()) {
        emit taskStateChanged(taskId, state);

        if (shouldEmitFinished) {
            emit taskFinished(taskId, state == testd::Task::State::FINISHED);
        }

        if (shouldEmitAllFinished) {
            emit allTasksFinished();
        }
    }
}

void TaskManager::onTaskProgressUpdated(const QString& message) {
    testd::Task* task = qobject_cast<testd::Task*>(sender());
    if (!task) return;
    
    // 查找任务ID
    QString taskId;
    {
        QMutexLocker locker(&tasksMutex_);
        for (auto it = tasks_.begin(); it != tasks_.end(); ++it) {
            if (it.value().get() == task) {
                taskId = it.key();
                break;
            }
        }
    }
    
    if (!taskId.isEmpty()) {
        emit taskProgressUpdated(taskId, message);
    }
}

void TaskManager::onTaskFinished(bool success) {
    Q_UNUSED(success)
    // 由onTaskStateChanged处理
}

void TaskManager::onTaskError(const QString& error) {
    testd::Task* task = qobject_cast<testd::Task*>(sender());
    if (!task) return;
    
    // 查找任务ID
    QString taskId;
    {
        QMutexLocker locker(&tasksMutex_);
        for (auto it = tasks_.begin(); it != tasks_.end(); ++it) {
            if (it.value().get() == task) {
                taskId = it.key();
                break;
            }
        }
    }
    
    if (!taskId.isEmpty()) {
        emit taskError(taskId, error);
    }
}

QString TaskManager::generateTaskId() const {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

void TaskManager::connectTaskSignals(std::shared_ptr<testd::Task> task, const QString& taskId) {
    Q_UNUSED(taskId)
    
    connect(task.get(), &testd::Task::stateChanged,
            this, &TaskManager::onTaskStateChanged);
    connect(task.get(), &testd::Task::progressUpdated,
            this, &TaskManager::onTaskProgressUpdated);
    connect(task.get(), &testd::Task::finished,
            this, &TaskManager::onTaskFinished);
    connect(task.get(), &testd::Task::errorOccurred,
            this, &TaskManager::onTaskError);
}

void TaskManager::disconnectTaskSignals(std::shared_ptr<testd::Task> task) {
    if (task) {
        disconnect(task.get(), nullptr, this, nullptr);
    }
}

bool TaskManager::canStartNewTask() const {
    return runningTasks_.size() < maxConcurrentTasks_;
}

// TaskStatusDisplay implementation
TaskStatusDisplay::TaskStatusDisplay(TaskManager* taskManager, QObject* parent)
    : QObject(parent)
    , taskManager_(taskManager)
    , enabled_(true)
    , autoRefresh_(false)
    , refreshTimer_(new QTimer(this))
{
    refreshTimer_->setInterval(1000); // 1秒刷新一次
    connect(refreshTimer_, &QTimer::timeout, this, &TaskStatusDisplay::onAutoRefresh);

    // 连接任务管理器信号
    connect(taskManager_, &TaskManager::taskAdded,
            this, &TaskStatusDisplay::onTaskAdded);
    connect(taskManager_, &TaskManager::taskStateChanged,
            this, &TaskStatusDisplay::onTaskStateChanged);
    connect(taskManager_, &TaskManager::taskProgressUpdated,
            this, &TaskStatusDisplay::onTaskProgressUpdated);
}

TaskStatusDisplay::~TaskStatusDisplay() = default;

void TaskStatusDisplay::setEnabled(bool enabled) {
    enabled_ = enabled;
}

bool TaskStatusDisplay::isEnabled() const {
    return enabled_;
}

void TaskStatusDisplay::setAutoRefresh(bool autoRefresh) {
    autoRefresh_ = autoRefresh;
    if (autoRefresh_) {
        refreshTimer_->start();
    } else {
        refreshTimer_->stop();
    }
}

bool TaskStatusDisplay::isAutoRefresh() const {
    return autoRefresh_;
}

void TaskStatusDisplay::setRefreshInterval(int milliseconds) {
    refreshTimer_->setInterval(milliseconds);
}

int TaskStatusDisplay::getRefreshInterval() const {
    return refreshTimer_->interval();
}

QList<TaskStatusDisplay::TaskDisplayInfo> TaskStatusDisplay::getTaskDisplayInfo() const {
    QMutexLocker locker(&cacheMutex_);
    return displayCache_.values();
}

TaskStatusDisplay::TaskDisplayInfo TaskStatusDisplay::getTaskDisplayInfo(const QString& taskId) const {
    QMutexLocker locker(&cacheMutex_);
    return displayCache_.value(taskId);
}

void TaskStatusDisplay::refresh() {
    if (!enabled_) return;

    QStringList allTasks = taskManager_->getAllTasks();
    for (const QString& taskId : allTasks) {
        updateDisplayCache(taskId);
    }

    emit displayUpdated();
}

void TaskStatusDisplay::clear() {
    QMutexLocker locker(&cacheMutex_);
    displayCache_.clear();
    emit displayUpdated();
}

void TaskStatusDisplay::onTaskAdded(const QString& taskId, const QString& name) {
    Q_UNUSED(name)
    updateDisplayCache(taskId);
}

void TaskStatusDisplay::onTaskStateChanged(const QString& taskId, testd::Task::State state) {
    Q_UNUSED(state)
    updateDisplayCache(taskId);
}

void TaskStatusDisplay::onTaskProgressUpdated(const QString& taskId, const QString& message) {
    Q_UNUSED(message)
    updateDisplayCache(taskId);
}

void TaskStatusDisplay::onAutoRefresh() {
    refresh();
}

QString TaskStatusDisplay::formatDuration(const QDateTime& start, const QDateTime& end) const {
    if (!start.isValid()) return "";

    QDateTime endTime = end.isValid() ? end : QDateTime::currentDateTime();
    qint64 seconds = start.secsTo(endTime);

    if (seconds < 60) {
        return QString("%1s").arg(seconds);
    } else if (seconds < 3600) {
        return QString("%1m %2s").arg(seconds / 60).arg(seconds % 60);
    } else {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        return QString("%1h %2m").arg(hours).arg(minutes);
    }
}

QString TaskStatusDisplay::formatState(testd::Task::State state) const {
    switch (state) {
    case testd::Task::State::CREATE:
        return "创建";
    case testd::Task::State::INIT:
        return "初始化";
    case testd::Task::State::RUNNING:
        return "运行中";
    case testd::Task::State::FINISHED:
        return "完成";
    case testd::Task::State::ERROR:
        return "错误";
    default:
        return "未知";
    }
}

int TaskStatusDisplay::calculateProgress(std::shared_ptr<testd::Task> task) const {
    if (!task) return 0;
    return taskManager_->getTaskProgress(taskManager_->getAllTasks().first()); // 简化实现
}

void TaskStatusDisplay::updateDisplayCache(const QString& taskId) {
    // 使用QTimer::singleShot延迟执行，避免在信号处理中直接调用可能加锁的方法
    QTimer::singleShot(0, this, [this, taskId]() {
        auto task = taskManager_->getTask(taskId);
        if (!task) return;

        TaskDisplayInfo info;
        info.id = taskId;
        info.name = QString::fromStdString(task->getName());
        info.description = QString::fromStdString(task->getDescription());
        info.state = task->getState();
        info.stateText = formatState(info.state);
        info.progressPercent = taskManager_->getTaskProgress(taskId);

        // 时间信息
        auto startTime = task->getStartTime();
        auto endTime = task->getEndTime();

        if (startTime.time_since_epoch().count() > 0) {
            info.startTime = QDateTime::fromMSecsSinceEpoch(
                std::chrono::duration_cast<std::chrono::milliseconds>(
                    startTime.time_since_epoch()).count());
        }

        if (endTime.time_since_epoch().count() > 0) {
            info.endTime = QDateTime::fromMSecsSinceEpoch(
                std::chrono::duration_cast<std::chrono::milliseconds>(
                    endTime.time_since_epoch()).count());
        }

        info.duration = formatDuration(info.startTime, info.endTime);

        // 子任务信息
        const auto& subTasks = task->getSubTasks();
        info.hasSubTasks = !subTasks.empty();
        info.subTaskCount = subTasks.size();
        info.completedSubTasks = 0;

        for (const auto& subTask : subTasks) {
            if (subTask->getState() == testd::Task::State::FINISHED) {
                info.completedSubTasks++;
            }
        }

        {
            QMutexLocker locker(&cacheMutex_);
            displayCache_[taskId] = info;
        }

        emit taskDisplayInfoChanged(taskId, info);
    });
}
