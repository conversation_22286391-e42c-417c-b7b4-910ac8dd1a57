#include "models.h"
#include <QJsonDocument>
#include <QDebug>

ChangeInfo ChangeInfo::fromJson(const QJsonObject& json) {
    ChangeInfo change;
    change.id = json["id"].toString();
    change.changeId = json["change_id"].toString();
    change.project = json["project"].toString();
    change.branch = json["branch"].toString();
    change.subject = json["subject"].toString();
    change.status = json["status"].toString();
    change.number = json["_number"].toInt();
    change.mergeable = json["mergeable"].toBool();
    change.topic = json["topic"].toString();
    
    // 解析时间
    QString createdStr = json["created"].toString();
    QString updatedStr = json["updated"].toString();
    change.created = QDateTime::fromString(createdStr, Qt::ISODate);
    change.updated = QDateTime::fromString(updatedStr, Qt::ISODate);
    
    // 解析owner
    QJsonObject ownerObj = json["owner"].toObject();
    change.owner = ownerObj["name"].toString();
    if (change.owner.isEmpty()) {
        change.owner = ownerObj["email"].toString();
    }
    
    // 解析标签
    change.labels = json["labels"].toObject();
    
    // 解析评审者
    change.reviewers = json["reviewers"].toArray();
    
    return change;
}

QJsonObject ChangeInfo::toJson() const {
    QJsonObject json;
    json["id"] = id;
    json["change_id"] = changeId;
    json["project"] = project;
    json["branch"] = branch;
    json["subject"] = subject;
    json["status"] = status;
    json["owner"] = owner;
    json["_number"] = number;
    json["mergeable"] = mergeable;
    json["topic"] = topic;
    json["created"] = created.toString(Qt::ISODate);
    json["updated"] = updated.toString(Qt::ISODate);
    json["labels"] = labels;
    json["reviewers"] = reviewers;
    return json;
}

LabelInfo LabelInfo::fromJson(const QString& name, const QJsonObject& json) {
    LabelInfo label;
    label.name = name;
    label.value = json["value"].toInt();
    label.minValue = json["default_value"].toInt();
    label.maxValue = json["default_value"].toInt();
    label.blocking = json["blocking"].toBool();
    label.all = json["all"].toArray();
    
    // 计算min/max值
    QStringList valueKeys = json["values"].toObject().keys();
    if (!valueKeys.isEmpty()) {
        QStringList valueStrs = valueKeys;
        valueStrs.sort();
        if (!valueStrs.isEmpty()) {
            label.minValue = valueStrs.first().toInt();
            label.maxValue = valueStrs.last().toInt();
        }
    }
    
    return label;
}

QueryBuilder::QueryBuilder() {
}

QueryBuilder& QueryBuilder::owner(const QString& user) {
    conditions_ << QString("owner:%1").arg(user);
    return *this;
}

QueryBuilder& QueryBuilder::reviewer(const QString& user) {
    conditions_ << QString("reviewer:%1").arg(user);
    return *this;
}

QueryBuilder& QueryBuilder::project(const QString& project) {
    conditions_ << QString("project:%1").arg(project);
    return *this;
}

QueryBuilder& QueryBuilder::branch(const QString& branch) {
    conditions_ << QString("branch:%1").arg(branch);
    return *this;
}

QueryBuilder& QueryBuilder::status(const QString& status) {
    conditions_ << QString("status:%1").arg(status);
    return *this;
}

QueryBuilder& QueryBuilder::topic(const QString& topic) {
    conditions_ << QString("topic:%1").arg(topic);
    return *this;
}

QueryBuilder& QueryBuilder::attention(const QString& user) {
    conditions_ << QString("attention:%1").arg(user);
    return *this;
}

QueryBuilder& QueryBuilder::after(const QDateTime& date) {
    conditions_ << QString("after:%1").arg(date.toString("yyyy-MM-dd"));
    return *this;
}

QueryBuilder& QueryBuilder::before(const QDateTime& date) {
    conditions_ << QString("before:%1").arg(date.toString("yyyy-MM-dd"));
    return *this;
}

QueryBuilder& QueryBuilder::label(const QString& label, const QString& value) {
    conditions_ << QString("label:%1%2").arg(label, value);
    return *this;
}

QueryBuilder& QueryBuilder::custom(const QString& condition) {
    conditions_ << condition;
    return *this;
}

QString QueryBuilder::build() const {
    return conditions_.join(" ");
}

QString QueryBuilder::myChanges() {
    return "attention:self";
}

QString QueryBuilder::myDrafts() {
    return "owner:self is:draft";
}

QString QueryBuilder::myReviews() {
    return "reviewer:self -owner:self";
}

QString QueryBuilder::openChanges(const QString& project) {
    if (project.isEmpty()) {
        return "status:open";
    }
    return QString("status:open project:%1").arg(project);
}

AISummaryConfig AISummaryConfig::defaultConfig() {
    AISummaryConfig config;
    config.endpoint = "http://localhost:11434/api/generate";
    config.model = "llama2";
    config.apiKey = "";
    config.promptTemplate = "请总结以下代码变更的内容和影响：\n\n{diff}\n\n请用中文回答，包括：\n1. 主要变更内容\n2. 可能的影响\n3. 需要注意的问题";
    config.maxTokens = 2048;
    config.temperature = 0.7;
    return config;
}

QJsonObject AISummaryConfig::toJson() const {
    QJsonObject json;
    json["endpoint"] = endpoint;
    json["model"] = model;
    json["apiKey"] = apiKey;
    json["promptTemplate"] = promptTemplate;
    json["maxTokens"] = maxTokens;
    json["temperature"] = temperature;
    return json;
}

AISummaryConfig AISummaryConfig::fromJson(const QJsonObject& json) {
    AISummaryConfig config;
    config.endpoint = json["endpoint"].toString();
    config.model = json["model"].toString();
    config.apiKey = json["apiKey"].toString();
    config.promptTemplate = json["promptTemplate"].toString();
    config.maxTokens = json["maxTokens"].toInt(2048);
    config.temperature = json["temperature"].toDouble(0.7);
    return config;
}

GerritConfig GerritConfig::defaultConfig() {
    GerritConfig config;
    config.serverUrl = "http://localhost:8080";
    config.username = "";
    config.password = "";
    config.useHttps = false;
    config.timeout = 30;
    return config;
}

QJsonObject GerritConfig::toJson() const {
    QJsonObject json;
    json["serverUrl"] = serverUrl;
    json["username"] = username;
    json["password"] = password;
    json["useHttps"] = useHttps;
    json["timeout"] = timeout;
    return json;
}

GerritConfig GerritConfig::fromJson(const QJsonObject& json) {
    GerritConfig config;
    config.serverUrl = json["serverUrl"].toString();
    config.username = json["username"].toString();
    config.password = json["password"].toString();
    config.useHttps = json["useHttps"].toBool();
    config.timeout = json["timeout"].toInt(30);
    return config;
}
