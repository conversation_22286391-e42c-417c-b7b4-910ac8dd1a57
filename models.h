#ifndef MODELS_H
#define MODELS_H

#include <QString>
#include <QDateTime>
#include <QJsonObject>
#include <QJsonArray>
#include <QList>
#include <QVariant>

/**
 * @brief Gerrit变更信息模型
 */
struct ChangeInfo {
    QString id;                    // Change ID
    QString changeId;              // Change-Id (用于Cherry-Pick状态检查)
    QString project;               // 项目名
    QString branch;                // 分支名
    QString subject;               // 提交主题
    QString status;                // 状态: NEW, MERGED, ABANDONED
    QString owner;                 // 提交者
    QDateTime created;             // 创建时间
    QDateTime updated;             // 更新时间
    int number;                    // 变更号
    bool hasReviewed;              // 是否已评审
    bool mergeable;                // 是否可合并
    QString topic;                 // 主题
    QJsonObject labels;            // 标签信息(Code-Review, Verified等)
    QJsonArray reviewers;          // 评审者列表
    QString diff;                  // diff内容(按需加载)
    
    // Cherry-Pick状态相关
    QList<ChangeInfo> relatedChanges;  // 相同Change-Id在其他分支的变更
    
    static ChangeInfo fromJson(const QJsonObject& json);
    QJsonObject toJson() const;
};

/**
 * @brief 评审标签信息
 */
struct LabelInfo {
    QString name;                  // 标签名(如Code-Review)
    int value;                     // 当前值
    int minValue;                  // 最小值
    int maxValue;                  // 最大值
    bool blocking;                 // 是否阻塞
    QJsonArray all;                // 所有评分记录
    
    static LabelInfo fromJson(const QString& name, const QJsonObject& json);
};

/**
 * @brief 查询条件构建器
 */
class QueryBuilder {
public:
    QueryBuilder();
    
    // 基础查询条件
    QueryBuilder& owner(const QString& user);
    QueryBuilder& reviewer(const QString& user);
    QueryBuilder& project(const QString& project);
    QueryBuilder& branch(const QString& branch);
    QueryBuilder& status(const QString& status);
    QueryBuilder& topic(const QString& topic);
    QueryBuilder& attention(const QString& user);
    
    // 时间条件
    QueryBuilder& after(const QDateTime& date);
    QueryBuilder& before(const QDateTime& date);
    
    // 标签条件
    QueryBuilder& label(const QString& label, const QString& value);
    
    // 自定义条件
    QueryBuilder& custom(const QString& condition);
    
    // 构建查询字符串
    QString build() const;
    
    // 预定义查询
    static QString myChanges();                    // attention:self
    static QString myDrafts();                     // owner:self is:draft
    static QString myReviews();                    // reviewer:self -owner:self
    static QString openChanges(const QString& project = "");
    
private:
    QStringList conditions_;
};

/**
 * @brief AI总结配置
 */
struct AISummaryConfig {
    QString endpoint;              // AI服务端点
    QString model;                 // 模型名称
    QString apiKey;                // API密钥
    QString promptTemplate;        // 提示词模板
    int maxTokens;                 // 最大token数
    double temperature;            // 温度参数
    
    static AISummaryConfig defaultConfig();
    QJsonObject toJson() const;
    static AISummaryConfig fromJson(const QJsonObject& json);
};

/**
 * @brief Gerrit服务器配置
 */
struct GerritConfig {
    QString serverUrl;             // 服务器URL
    QString username;              // 用户名
    QString password;              // 密码或token
    bool useHttps;                 // 是否使用HTTPS
    int timeout;                   // 超时时间(秒)
    
    static GerritConfig defaultConfig();
    QJsonObject toJson() const;
    static GerritConfig fromJson(const QJsonObject& json);
};

#endif // MODELS_H
