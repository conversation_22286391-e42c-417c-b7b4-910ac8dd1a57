<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1045</width>
    <height>595</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QListWidget" name="listWidget_changes">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
     </widget>
    </item>
    <item row="0" column="1">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>AI</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QTextBrowser" name="textBrowser_ai"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_2">
       <attribute name="title">
        <string>Info</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QTreeView" name="treeView_info"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_3">
       <attribute name="title">
        <string>Data</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="0">
         <widget class="QTextBrowser" name="textBrowser_data"/>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item row="1" column="0" colspan="2">
     <widget class="QLineEdit" name="lineEdit_command"/>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1045</width>
     <height>24</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>设置</string>
    </property>
    <addaction name="action_Ollama"/>
    <addaction name="action_Gerrit"/>
    <addaction name="action_AI"/>
   </widget>
   <widget class="QMenu" name="menu_2">
    <property name="title">
     <string>编辑</string>
    </property>
    <addaction name="action_1"/>
   </widget>
   <addaction name="menu"/>
   <addaction name="menu_2"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="action_Ollama">
   <property name="text">
    <string>设置Ollama凭据</string>
   </property>
  </action>
  <action name="action_Gerrit">
   <property name="text">
    <string>设置Gerrit凭据</string>
   </property>
  </action>
  <action name="action_1">
   <property name="text">
    <string>选中项全部+1</string>
   </property>
  </action>
  <action name="action_AI">
   <property name="text">
    <string>设置AI模板</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
