#!/bin/bash

# Gerrit测试环境停止脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GERRIT_SITE="$SCRIPT_DIR/gerrit-site"
GERRIT_PORT=8080

echo "=== Gerrit测试环境停止脚本 ==="

# 查找Gerrit进程
GERRIT_PID=$(ps aux | grep "gerrit.*daemon" | grep -v grep | awk '{print $2}')

if [ -z "$GERRIT_PID" ]; then
    echo "未找到运行中的Gerrit进程"
else
    echo "找到Gerrit进程: $GERRIT_PID"
    echo "正在停止Gerrit..."
    
    kill "$GERRIT_PID"
    
    # 等待进程结束
    for i in {1..10}; do
        if ! kill -0 "$GERRIT_PID" 2>/dev/null; then
            echo "Gerrit已成功停止"
            break
        fi
        echo "等待Gerrit停止... ($i/10)"
        sleep 1
    done
    
    # 如果进程仍在运行，强制杀死
    if kill -0 "$GERRIT_PID" 2>/dev/null; then
        echo "强制停止Gerrit..."
        kill -9 "$GERRIT_PID"
        sleep 1
        
        if kill -0 "$GERRIT_PID" 2>/dev/null; then
            echo "错误: 无法停止Gerrit进程"
            exit 1
        else
            echo "Gerrit已强制停止"
        fi
    fi
fi

# 检查端口是否仍被占用
if netstat -tuln 2>/dev/null | grep -q ":$GERRIT_PORT "; then
    echo "警告: 端口 $GERRIT_PORT 仍被占用"
    
    # 查找占用端口的进程
    PORT_PID=$(netstat -tulnp 2>/dev/null | grep ":$GERRIT_PORT " | awk '{print $7}' | cut -d'/' -f1)
    if [ -n "$PORT_PID" ]; then
        echo "端口被进程 $PORT_PID 占用"
        read -p "是否强制杀死该进程? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kill -9 "$PORT_PID"
            echo "进程已被强制杀死"
        fi
    fi
else
    echo "端口 $GERRIT_PORT 已释放"
fi

echo "Gerrit停止脚本执行完成"
