#!/bin/bash

# Gerrit测试数据初始化脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GERRIT_URL="http://localhost:8080"
GERRIT_USER="admin"
TEST_PROJECT="test-project"
TEST_REPO_DIR="$SCRIPT_DIR/test-repo"

echo "=== Gerrit测试数据初始化脚本 ==="

# 检查Gerrit是否运行
if ! curl -s "$GERRIT_URL" > /dev/null; then
    echo "错误: Gerrit服务器未运行，请先启动Gerrit"
    echo "运行: ./start-gerrit.sh"
    exit 1
fi

echo "Gerrit服务器运行正常: $GERRIT_URL"

# 检查Git环境
if ! command -v git &> /dev/null; then
    echo "错误: 未找到Git，请安装Git"
    exit 1
fi

echo "Git版本: $(git --version)"

# 等待Gerrit完全启动
echo "等待Gerrit完全启动..."
for i in {1..30}; do
    if curl -s "$GERRIT_URL/config/server/version" > /dev/null; then
        echo "Gerrit已就绪"
        break
    fi
    echo "等待中... ($i/30)"
    sleep 2
done

# 配置Git用户信息
git config --global user.name "Test User"
git config --global user.email "<EMAIL>"

echo "Git用户配置完成"

# 创建测试项目
echo "创建测试项目: $TEST_PROJECT"

# 使用Gerrit REST API创建项目
curl -X PUT \
    -H "Content-Type: application/json" \
    -d '{"description": "Test project for Gerrit client", "create_empty_commit": true}' \
    "$GERRIT_URL/a/projects/$TEST_PROJECT" \
    --user "$GERRIT_USER:" \
    -v

if [ $? -eq 0 ]; then
    echo "测试项目创建成功"
else
    echo "测试项目可能已存在或创建失败"
fi

# 克隆测试项目
if [ -d "$TEST_REPO_DIR" ]; then
    echo "删除现有测试仓库目录"
    rm -rf "$TEST_REPO_DIR"
fi

echo "克隆测试项目..."
git clone "$GERRIT_URL/$TEST_PROJECT" "$TEST_REPO_DIR"

if [ $? -ne 0 ]; then
    echo "错误: 无法克隆测试项目"
    exit 1
fi

cd "$TEST_REPO_DIR"

# 安装commit-msg hook
echo "安装commit-msg hook..."
curl -Lo .git/hooks/commit-msg "$GERRIT_URL/tools/hooks/commit-msg"
chmod +x .git/hooks/commit-msg

# 创建测试提交
echo "创建测试提交..."

# 第一个测试提交
echo "# Test Project

This is a test project for Gerrit client testing.

## Features
- Test feature 1
- Test feature 2
" > README.md

git add README.md
git commit -m "Add README file

This commit adds a basic README file to the test project.

Change-Id: I$(openssl rand -hex 20)"

# 第二个测试提交
echo "def hello_world():
    print('Hello, World!')

if __name__ == '__main__':
    hello_world()
" > hello.py

git add hello.py
git commit -m "Add hello world script

This commit adds a simple Python hello world script.

Change-Id: I$(openssl rand -hex 20)"

# 第三个测试提交
echo "# Test Project

This is a test project for Gerrit client testing.

## Features
- Test feature 1
- Test feature 2
- Test feature 3

## Usage
Run the hello world script:
\`\`\`
python hello.py
\`\`\`
" > README.md

git add README.md
git commit -m "Update README with usage instructions

This commit updates the README file with usage instructions
for the hello world script.

Change-Id: I$(openssl rand -hex 20)"

# 推送到Gerrit进行评审
echo "推送提交到Gerrit进行评审..."
git push origin HEAD:refs/for/master

if [ $? -eq 0 ]; then
    echo "测试提交推送成功"
else
    echo "警告: 测试提交推送失败"
fi

echo ""
echo "=== 测试数据初始化完成 ==="
echo "Gerrit URL: $GERRIT_URL"
echo "测试项目: $TEST_PROJECT"
echo "测试仓库: $TEST_REPO_DIR"
echo ""
echo "你可以在浏览器中访问 $GERRIT_URL 查看Gerrit界面"
echo "使用开发模式，可以以任何用户身份登录"
echo ""
echo "测试客户端配置:"
echo "- 服务器URL: $GERRIT_URL"
echo "- 用户名: admin (或任何用户名)"
echo "- 密码: (留空)"
