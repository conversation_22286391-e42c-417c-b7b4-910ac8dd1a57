#include "gerritclient.h"
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QUrlQuery>
#include <QAuthenticator>
#include <QDebug>
#include <QEventLoop>

GerritClient::GerritClient(QObject* parent)
    : QObject(parent)
    , networkManager_(new QNetworkAccessManager(this))
    , config_(GerritConfig::defaultConfig())
{
}

GerritClient::~GerritClient() = default;

void GerritClient::setConfig(const GerritConfig& config) {
    config_ = config;
}

GerritConfig GerritClient::getConfig() const {
    return config_;
}

std::shared_ptr<testd::Task> GerritClient::queryChanges(const QString& query, int limit) {
    QString path = QString("/changes/?q=%1&n=%2&o=DETAILED_LABELS&o=DETAILED_ACCOUNTS&o=REVIEWER_UPDATES")
                   .arg(QString(QUrl::toPercentEncoding(query)), QString::number(limit));
    
    return createNetworkTask(
        "Query Changes",
        QString("Querying changes: %1").arg(query),
        path
    );
}

std::shared_ptr<testd::Task> GerritClient::getChangeDetail(const QString& changeId) {
    QString path = QString("/changes/%1/detail?o=DETAILED_LABELS&o=DETAILED_ACCOUNTS&o=REVIEWER_UPDATES")
                   .arg(changeId);
    
    return createNetworkTask(
        "Get Change Detail",
        QString("Getting details for change: %1").arg(changeId),
        path
    );
}

std::shared_ptr<testd::Task> GerritClient::getChangeDiff(const QString& changeId) {
    QString path = QString("/changes/%1/revisions/current/patch?format=TEXT")
                   .arg(changeId);
    
    return createNetworkTask(
        "Get Change Diff",
        QString("Getting diff for change: %1").arg(changeId),
        path
    );
}

std::shared_ptr<testd::Task> GerritClient::getRelatedChanges(const QString& changeId) {
    // 首先获取change详情以得到change-id
    auto detailTask = getChangeDetail(changeId);
    
    auto relatedTask = testd::LambdaTask::create(
        "Get Related Changes",
        QString("Getting related changes for: %1").arg(changeId).toStdString(),
        [this, changeId](testd::LambdaTask* task, testd::Task::State state, const QJsonObject& data) -> int {
            Q_UNUSED(data)
            
            if (state == testd::Task::State::RUNNING) {
                // 这里应该从detailTask的结果中获取change-id，然后查询相关变更
                // 简化实现：直接返回成功
                emit relatedChangesReceived(changeId, QList<ChangeInfo>());
                return 0;
            }
            return -EAGAIN;
        }
    );
    
    relatedTask->addSubTask(detailTask);
    return relatedTask;
}

std::shared_ptr<testd::Task> GerritClient::reviewChange(const QString& changeId, int score, const QString& message) {
    QJsonObject labels;
    labels["Code-Review"] = score;
    
    QJsonObject payload;
    payload["labels"] = labels;
    if (!message.isEmpty()) {
        payload["message"] = message;
    }
    
    QString path = QString("/changes/%1/revisions/current/review").arg(changeId);
    
    return createNetworkTask(
        "Review Change",
        QString("Reviewing change %1 with score %2").arg(changeId).arg(score),
        path,
        "POST",
        payload
    );
}

std::shared_ptr<testd::Task> GerritClient::batchReview(const QStringList& changeIds, int score, const QString& message) {
    auto batchTask = testd::Task::create(
        "Batch Review",
        QString("Batch reviewing %1 changes with score %2").arg(changeIds.size()).arg(score).toStdString(),
        testd::Task::SubtaskRunMode::PARALLEL,
        false
    );
    
    for (const QString& changeId : changeIds) {
        auto reviewTask = reviewChange(changeId, score, message);
        batchTask->addSubTask(reviewTask);
    }
    
    return batchTask;
}

std::shared_ptr<testd::Task> GerritClient::testConnection() {
    return createNetworkTask(
        "Test Connection",
        "Testing connection to Gerrit server",
        "/config/server/version"
    );
}

QNetworkRequest GerritClient::buildRequest(const QString& path) const {
    QNetworkRequest request;
    request.setUrl(QUrl(buildUrl(path)));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    
    // 添加认证头
    QByteArray authHeader = buildAuthHeader();
    if (!authHeader.isEmpty()) {
        request.setRawHeader("Authorization", authHeader);
    }
    
    return request;
}

QString GerritClient::buildUrl(const QString& path) const {
    QString baseUrl = config_.serverUrl;
    if (!baseUrl.endsWith("/")) {
        baseUrl += "/";
    }
    baseUrl += "a"; // Gerrit REST API prefix
    return baseUrl + path;
}

QByteArray GerritClient::buildAuthHeader() const {
    if (config_.username.isEmpty() || config_.password.isEmpty()) {
        return QByteArray();
    }
    
    QString credentials = QString("%1:%2").arg(config_.username, config_.password);
    QByteArray encodedCredentials = credentials.toUtf8().toBase64();
    return "Basic " + encodedCredentials;
}

std::shared_ptr<testd::LambdaTask> GerritClient::createNetworkTask(
    const QString& name,
    const QString& description,
    const QString& path,
    const QString& method,
    const QJsonObject& payload)
{
    return testd::LambdaTask::create(
        name.toStdString(),
        description.toStdString(),
        [this, path, method, payload](testd::LambdaTask* task, testd::Task::State state, const QJsonObject& data) -> int {
            Q_UNUSED(data)

            if (state == testd::Task::State::INIT) {
                // 检查配置
                if (config_.serverUrl.isEmpty()) {
                    task->setResultString("Gerrit server URL not configured");
                    return -1;
                }

                QNetworkRequest request = buildRequest(path);
                QNetworkReply* reply = nullptr;
                
                if (method == "GET") {
                    reply = networkManager_->get(request);
                } else if (method == "POST") {
                    QJsonDocument doc(payload);
                    reply = networkManager_->post(request, doc.toJson());
                } else if (method == "PUT") {
                    QJsonDocument doc(payload);
                    reply = networkManager_->put(request, doc.toJson());
                }
                
                if (reply) {
                    // 连接信号处理响应
                    connect(reply, &QNetworkReply::finished, this, &GerritClient::handleNetworkReply);
//                    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
//                            this, &GerritClient::handleNetworkError);
                    return 0; // 任务完成
                } else {
                    task->setResultString("Failed to create network request");
                    return -1; // 错误
                }
            }
            
            return -EAGAIN; // 继续等待
        }
    );
}

void GerritClient::handleNetworkReply() {
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;
    
    reply->deleteLater();
    
    if (reply->error() != QNetworkReply::NoError) {
        emit errorOccurred(formatNetworkError(reply->error()));
        return;
    }
    
    QByteArray data = reply->readAll();
    
    // 解析响应
    QString path = reply->url().path();
    if (path.contains("/changes/")) {
        if (path.contains("/patch")) {
            // Diff响应
            QString changeId = path.split("/")[2];
            emit diffReceived(changeId, QString::fromUtf8(data));
        } else if (path.endsWith("/detail")) {
            // Change detail响应
            QJsonObject changeObj = parseGerritResponse(data);
            ChangeInfo change = ChangeInfo::fromJson(changeObj);
            emit changeDetailReceived(change);
        } else {
            // Changes list响应
            QJsonArray changesArray = parseGerritArrayResponse(data);
            QList<ChangeInfo> changes;
            for (const auto& value : changesArray) {
                changes.append(ChangeInfo::fromJson(value.toObject()));
            }
            emit changesReceived(changes);
        }
    } else if (path.contains("/review")) {
        // Review响应
        QString changeId = path.split("/")[2];
        emit reviewSubmitted(changeId, true);
    } else if (path.contains("/version")) {
        // Version响应
        emit connectionTested(true, "Connection successful");
    }
}

void GerritClient::handleNetworkError(QNetworkReply::NetworkError error) {
    emit errorOccurred(formatNetworkError(error));
}

QJsonObject GerritClient::parseGerritResponse(const QByteArray& data) const {
    // Gerrit响应通常以")]}'"开头，需要去除
    QByteArray cleanData = data;
    if (cleanData.startsWith(")]}'\n")) {
        cleanData = cleanData.mid(5);
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(cleanData, &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return QJsonObject();
    }
    
    return doc.object();
}

QJsonArray GerritClient::parseGerritArrayResponse(const QByteArray& data) const {
    // Gerrit响应通常以")]}'"开头，需要去除
    QByteArray cleanData = data;
    if (cleanData.startsWith(")]}'\n")) {
        cleanData = cleanData.mid(5);
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(cleanData, &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return QJsonArray();
    }
    
    return doc.array();
}

QString GerritClient::formatNetworkError(QNetworkReply::NetworkError error) const {
    switch (error) {
    case QNetworkReply::ConnectionRefusedError:
        return "Connection refused";
    case QNetworkReply::RemoteHostClosedError:
        return "Remote host closed connection";
    case QNetworkReply::HostNotFoundError:
        return "Host not found";
    case QNetworkReply::TimeoutError:
        return "Request timeout";
    case QNetworkReply::AuthenticationRequiredError:
        return "Authentication required";
    default:
        return QString("Network error: %1").arg(static_cast<int>(error));
    }
}

// AISummaryClient implementation
AISummaryClient::AISummaryClient(QObject* parent)
    : QObject(parent)
    , networkManager_(new QNetworkAccessManager(this))
    , config_(AISummaryConfig::defaultConfig())
{
}

AISummaryClient::~AISummaryClient() = default;

void AISummaryClient::setConfig(const AISummaryConfig& config) {
    config_ = config;
}

AISummaryConfig AISummaryClient::getConfig() const {
    return config_;
}

std::shared_ptr<testd::Task> AISummaryClient::summarizeDiff(const QString& diff) {
    QString prompt = formatPrompt(diff);
    return createSummaryTask(
        "Summarize Diff",
        "Generating AI summary for diff",
        prompt
    );
}

std::shared_ptr<testd::Task> AISummaryClient::summarizeChange(const ChangeInfo& change) {
    QString prompt = formatPrompt(change.diff);
    return createSummaryTask(
        "Summarize Change",
        QString("Generating AI summary for change: %1").arg(change.subject),
        prompt
    );
}

std::shared_ptr<testd::LambdaTask> AISummaryClient::createSummaryTask(
    const QString& name,
    const QString& description,
    const QString& prompt)
{
    return testd::LambdaTask::create(
        name.toStdString(),
        description.toStdString(),
        [this, prompt](testd::LambdaTask* task, testd::Task::State state, const QJsonObject& data) -> int {
            Q_UNUSED(data)

            if (state == testd::Task::State::RUNNING) {
                QNetworkRequest request = buildRequest();
                QJsonObject payload = buildPayload(prompt);
                QJsonDocument doc(payload);

                QNetworkReply* reply = networkManager_->post(request, doc.toJson());

                if (reply) {
                    connect(reply, &QNetworkReply::finished, this, &AISummaryClient::handleNetworkReply);
//                    connect(reply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::errorOccurred),
//                            this, &AISummaryClient::handleNetworkError);
                    return 0;
                }

                return -1;
            }

            return -EAGAIN;
        }
    );
}

QNetworkRequest AISummaryClient::buildRequest() const {
    QNetworkRequest request;
    request.setUrl(QUrl(config_.endpoint));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    if (!config_.apiKey.isEmpty()) {
        request.setRawHeader("Authorization", QString("Bearer %1").arg(config_.apiKey).toUtf8());
    }

    return request;
}

QJsonObject AISummaryClient::buildPayload(const QString& prompt) const {
    QJsonObject payload;
    payload["model"] = config_.model;
    payload["prompt"] = prompt;
    payload["stream"] = true;
    payload["options"] = QJsonObject{
        {"temperature", config_.temperature},
        {"num_predict", config_.maxTokens}
    };

    return payload;
}

QString AISummaryClient::formatPrompt(const QString& diff) const {
    return config_.promptTemplate.arg(diff);
}

void AISummaryClient::handleNetworkReply() {
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) return;

    reply->deleteLater();

    if (reply->error() != QNetworkReply::NoError) {
        emit errorOccurred(QString("AI request failed: %1").arg(reply->errorString()));
        return;
    }

    QByteArray data = reply->readAll();
    parseStreamingResponse(data);
}

void AISummaryClient::handleNetworkError(QNetworkReply::NetworkError error) {
    emit errorOccurred(QString("AI network error: %1").arg(static_cast<int>(error)));
}

void AISummaryClient::parseStreamingResponse(const QByteArray& data) {
    // 解析流式响应（Ollama格式）
    QStringList lines = QString::fromUtf8(data).split('\n', Qt::SkipEmptyParts);
    QString fullResponse;

    for (const QString& line : lines) {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(line.toUtf8(), &error);
        if (error.error == QJsonParseError::NoError) {
            QJsonObject obj = doc.object();
            if (obj.contains("response")) {
                QString response = obj["response"].toString();
                fullResponse += response;
                emit summaryProgress(response);
            }
            if (obj["done"].toBool()) {
                emit summaryReceived(fullResponse);
                break;
            }
        }
    }
}
