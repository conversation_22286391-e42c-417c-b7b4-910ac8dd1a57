#!/bin/bash

# Gerrit测试环境启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GERRIT_WAR="$SCRIPT_DIR/gerrit-3.12.2.war"
GERRIT_SITE="$SCRIPT_DIR/gerrit-site"
GERRIT_PORT=8080

echo "=== Gerrit测试环境启动脚本 ==="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 11或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 11 ]; then
    echo "错误: Java版本过低，需要Java 11或更高版本"
    exit 1
fi

echo "Java版本检查通过: $(java -version 2>&1 | head -n1)"

# 检查Gerrit WAR文件
if [ ! -f "$GERRIT_WAR" ]; then
    echo "错误: 未找到Gerrit WAR文件: $GERRIT_WAR"
    exit 1
fi

echo "找到Gerrit WAR文件: $GERRIT_WAR"

# 创建Gerrit站点目录
if [ ! -d "$GERRIT_SITE" ]; then
    echo "初始化Gerrit站点..."
    mkdir -p "$GERRIT_SITE"
    
    # 初始化Gerrit站点
    java -jar "$GERRIT_WAR" init -d "$GERRIT_SITE" --batch --dev
    
    if [ $? -ne 0 ]; then
        echo "错误: Gerrit站点初始化失败"
        exit 1
    fi
    
    echo "Gerrit站点初始化完成"
    exit 0
else
    echo "使用现有Gerrit站点: $GERRIT_SITE"
fi

# 配置Gerrit
GERRIT_CONFIG="$GERRIT_SITE/etc/gerrit.config"
if [ -f "$GERRIT_CONFIG" ]; then
    echo "配置Gerrit..."
    
    # 设置HTTP端口
    sed -i "s/listenUrl = .*/listenUrl = http:\/\/*:$GERRIT_PORT\//" "$GERRIT_CONFIG"
    
    # 启用开发模式
    if ! grep -q "\[auth\]" "$GERRIT_CONFIG"; then
        echo "" >> "$GERRIT_CONFIG"
        echo "[auth]" >> "$GERRIT_CONFIG"
        echo "    type = DEVELOPMENT_BECOME_ANY_ACCOUNT" >> "$GERRIT_CONFIG"
    fi
    
    # 配置用户邮箱
    if ! grep -q "\[sendemail\]" "$GERRIT_CONFIG"; then
        echo "" >> "$GERRIT_CONFIG"
        echo "[sendemail]" >> "$GERRIT_CONFIG"
        echo "    enable = false" >> "$GERRIT_CONFIG"
    fi
    
    echo "Gerrit配置完成"
fi

# 检查端口是否被占用
if netstat -tuln 2>/dev/null | grep -q ":$GERRIT_PORT "; then
    echo "警告: 端口 $GERRIT_PORT 已被占用"
    echo "请检查是否已有Gerrit实例在运行"
    
    read -p "是否继续启动? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 启动Gerrit
echo "启动Gerrit服务器..."
echo "端口: $GERRIT_PORT"
echo "站点目录: $GERRIT_SITE"
echo ""

cd "$GERRIT_SITE"
java -jar "$GERRIT_WAR" daemon --console-log

echo ""
echo "Gerrit服务器已停止"
