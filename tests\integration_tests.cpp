#include <QtTest/QtTest>
#include <QApplication>
#include <QSignalSpy>
#include <QTimer>
#include <QClipboard>
#include <QSettings>
#include <QDir>
#include <QProcess>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <memory>
#include <qapplication.h>

#include "../models.h"
#include "../taskmanager.h"
#include "../gerritclient.h"
#include "../mainwindow.h"
#include "../task.hpp"

// 注册Qt元类型
Q_DECLARE_METATYPE(ChangeInfo)
Q_DECLARE_METATYPE(QList<ChangeInfo>)

class IntegrationTests : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 配置测试
    void testConfigFileReading();
    void testConfigFileCreation();
    void testGerritConfigValidation();

    // Gerrit集成测试
    void testGerritConnection();
    void testQueryChanges();
    void testChangeDetailRetrieval();
    void testDiffRetrieval();

    // 剪贴板功能测试
    void testClipboardUrlExtraction();
    void testBatchQueryFromClipboard();

    // 实际操作测试
    void testCreateTestCommit();
    void testPushAndQuery();
    void testInfoDataValidation();

    // UI集成测试
    void testMainWindowIntegration();
    void testCommandParsing();

    // 网络和错误处理测试
    void testNetworkErrorHandling();
    void testRepositoryConfiguration();

private:
    QApplication* app_;
    std::unique_ptr<MainWindow> mainWindow_;
    std::unique_ptr<GerritClient> gerritClient_;
    std::unique_ptr<TaskManager> taskManager_;
    
    GerritConfig testConfig_;
    QString testRepository_;
    QString testConfigPath_;
    QString testRepoPath_;
    
    // 辅助方法
    bool loadTestConfig();
    void createTestConfigFile();
    bool isGerritAvailable();
    QString createTestCommit();
    void waitForTask(const QString& taskId, int timeoutMs = 10000);
    void printConfigHelp();
};

void IntegrationTests::initTestCase()
{
    // 初始化测试环境
    // int argc = 0;
    // char* argv[] = {nullptr};
    // app_ = new QApplication(argc, argv);
    
    // 设置测试配置路径
    testConfigPath_ = QApplication::applicationDirPath() + "/../../tests/test_config.ini";
    testRepoPath_ = QDir::temp().filePath("gerrit_test_repo");
    
    qDebug() << "=== Gerrit Client Integration Tests ===";
    qDebug() << "Test config path:" << testConfigPath_;
    qDebug() << "Test repo path:" << testRepoPath_;
}

void IntegrationTests::cleanupTestCase()
{
    // 清理测试文件
    QDir(testRepoPath_).removeRecursively();
    
    delete app_;
}

void IntegrationTests::init()
{
    // 每个测试前的初始化
    taskManager_ = std::make_unique<TaskManager>();
    gerritClient_ = std::make_unique<GerritClient>();
}

void IntegrationTests::cleanup()
{
    // 每个测试后的清理
    mainWindow_.reset();
    gerritClient_.reset();
    taskManager_.reset();
}

void IntegrationTests::testConfigFileReading()
{
    // 测试配置文件读取
    if (!loadTestConfig()) {
        QSKIP("No test config available, creating template");
        createTestConfigFile();
        printConfigHelp();
        return;
    }
    
    QVERIFY(!testConfig_.serverUrl.isEmpty());
    QVERIFY(!testConfig_.username.isEmpty());
    
    qDebug() << "Loaded config:";
    qDebug() << "  Server:" << testConfig_.serverUrl;
    qDebug() << "  User:" << testConfig_.username;
    qDebug() << "  Has password:" << !testConfig_.password.isEmpty();
}

void IntegrationTests::testConfigFileCreation()
{
    // 测试配置文件创建
    QString tempConfigPath = QDir::temp().filePath("test_config_creation.ini");
    
    QSettings settings(tempConfigPath, QSettings::IniFormat);
    settings.beginGroup("Gerrit");
    settings.setValue("serverUrl", "http://localhost:8080");
    settings.setValue("username", "testuser");
    settings.setValue("password", "testpass");
    settings.endGroup();
    settings.sync();
    
    QVERIFY(QFile::exists(tempConfigPath));
    
    // 验证读取
    QSettings readSettings(tempConfigPath, QSettings::IniFormat);
    readSettings.beginGroup("Gerrit");
    QString serverUrl = readSettings.value("serverUrl").toString();
    QString username = readSettings.value("username").toString();
    readSettings.endGroup();
    
    QCOMPARE(serverUrl, QString("http://localhost:8080"));
    QCOMPARE(username, QString("testuser"));
    
    QFile::remove(tempConfigPath);
}

void IntegrationTests::testGerritConfigValidation()
{
    // 测试Gerrit配置验证
    GerritConfig config;
    
    // 空配置应该无效
    QVERIFY(config.serverUrl.isEmpty());
    QVERIFY(config.username.isEmpty());
    
    // 设置有效配置
    config.serverUrl = "http://localhost:8080";
    config.username = "admin";
    config.password = "";
    
    QVERIFY(!config.serverUrl.isEmpty());
    QVERIFY(!config.username.isEmpty());
    
    // 测试JSON序列化
    QJsonObject json = config.toJson();
    QCOMPARE(json["serverUrl"].toString(), config.serverUrl);
    QCOMPARE(json["username"].toString(), config.username);
    
    // 测试反序列化
    GerritConfig restored = GerritConfig::fromJson(json);
    QCOMPARE(restored.serverUrl, config.serverUrl);
    QCOMPARE(restored.username, config.username);
}

void IntegrationTests::testGerritConnection()
{
    if (!loadTestConfig()) {
        QSKIP("No test config available");
        return;
    }
    
    if (!isGerritAvailable()) {
        QSKIP("Gerrit server not available");
        return;
    }
    
    gerritClient_->setConfig(testConfig_);
    
    // 测试连接
    auto task = gerritClient_->testConnection();
    QString taskId = taskManager_->addTask(task);
    taskManager_->startTask(taskId);
    
    waitForTask(taskId);
    
    auto taskState = taskManager_->getTaskState(taskId);
    QCOMPARE(taskState, testd::Task::State::FINISHED);
}

void IntegrationTests::testQueryChanges()
{
    if (!loadTestConfig() || !isGerritAvailable()) {
        QSKIP("Test environment not available");
        return;
    }
    
    gerritClient_->setConfig(testConfig_);
    
    // 测试查询变更
    QString query = "status:open";
    auto task = gerritClient_->queryChanges(query);
    QString taskId = taskManager_->addTask(task);
    
    QSignalSpy changesSignal(gerritClient_.get(), &GerritClient::changesReceived);
    taskManager_->startTask(taskId);
    
    waitForTask(taskId);
    
    auto taskState = taskManager_->getTaskState(taskId);
    if (taskState == testd::Task::State::FINISHED) {
        // 如果有信号发出，验证数据
        if (changesSignal.count() > 0) {
            auto changes = changesSignal.at(0).at(0).value<QList<ChangeInfo>>();
            qDebug() << "Found" << changes.size() << "changes";
            
            for (const auto& change : changes) {
                QVERIFY(!change.id.isEmpty());
                QVERIFY(change.number > 0);
                QVERIFY(!change.subject.isEmpty());
                QVERIFY(!change.status.isEmpty());
                
                qDebug() << "Change:" << change.number << change.subject;
            }
        }
    } else {
        qWarning() << "Query task failed";
    }
}

void IntegrationTests::testChangeDetailRetrieval()
{
    if (!loadTestConfig() || !isGerritAvailable()) {
        QSKIP("Test environment not available");
        return;
    }
    
    gerritClient_->setConfig(testConfig_);
    
    // 首先查询获取一个变更ID
    auto queryTask = gerritClient_->queryChanges("status:open");
    QString queryTaskId = taskManager_->addTask(queryTask);
    
    QSignalSpy changesSignal(gerritClient_.get(), &GerritClient::changesReceived);
    taskManager_->startTask(queryTaskId);
    waitForTask(queryTaskId);
    
    if (changesSignal.count() > 0) {
        auto changes = changesSignal.at(0).at(0).value<QList<ChangeInfo>>();
        if (!changes.isEmpty()) {
            QString changeId = changes.first().id;
            
            // 测试获取变更详情
            auto detailTask = gerritClient_->getChangeDetail(changeId);
            QString detailTaskId = taskManager_->addTask(detailTask);
            
            QSignalSpy detailSignal(gerritClient_.get(), &GerritClient::changeDetailReceived);
            taskManager_->startTask(detailTaskId);
            waitForTask(detailTaskId);
            
            if (detailSignal.count() > 0) {
                auto change = detailSignal.at(0).at(0).value<ChangeInfo>();
                QCOMPARE(change.id, changeId);
                QVERIFY(!change.subject.isEmpty());
                qDebug() << "Retrieved detail for change:" << change.number << change.subject;
            }
        }
    }
}

void IntegrationTests::testDiffRetrieval()
{
    if (!loadTestConfig() || !isGerritAvailable()) {
        QSKIP("Test environment not available");
        return;
    }
    
    gerritClient_->setConfig(testConfig_);
    
    // 首先查询获取一个变更ID
    auto queryTask = gerritClient_->queryChanges("status:open");
    QString queryTaskId = taskManager_->addTask(queryTask);
    
    QSignalSpy changesSignal(gerritClient_.get(), &GerritClient::changesReceived);
    taskManager_->startTask(queryTaskId);
    waitForTask(queryTaskId);
    
    if (changesSignal.count() > 0) {
        auto changes = changesSignal.at(0).at(0).value<QList<ChangeInfo>>();
        if (!changes.isEmpty()) {
            QString changeId = changes.first().id;
            
            // 测试获取diff
            auto diffTask = gerritClient_->getChangeDiff(changeId);
            QString diffTaskId = taskManager_->addTask(diffTask);
            
            QSignalSpy diffSignal(gerritClient_.get(), &GerritClient::diffReceived);
            taskManager_->startTask(diffTaskId);
            waitForTask(diffTaskId);
            
            if (diffSignal.count() > 0) {
                QString diff = diffSignal.at(0).at(1).toString();
                QVERIFY(!diff.isEmpty());
                qDebug() << "Retrieved diff, length:" << diff.length();
                
                // 验证diff包含关键信息
                QVERIFY(diff.contains("diff") || diff.contains("@@") || diff.contains("---") || diff.contains("+++"));
            }
        }
    }
}

void IntegrationTests::testClipboardUrlExtraction()
{
    // 测试剪贴板URL提取功能
    QClipboard* clipboard = QApplication::clipboard();

    // 测试单个URL
    QString singleUrl = "http://localhost:8080/c/project/+/12345";
    clipboard->setText(singleUrl);

    // 这里需要实现URL提取逻辑（从MainWindow中提取）
    QRegularExpression changeUrlRegex(R"(/c/[^/]+/\+/(\d+))");
    QRegularExpressionMatch match = changeUrlRegex.match(singleUrl);
    QVERIFY(match.hasMatch());
    QString changeId = match.captured(1);
    QCOMPARE(changeId, QString("12345"));

    // 测试多个URL
    QString multipleUrls = R"(
    请查看以下变更：
    http://localhost:8080/c/project1/+/12345
    http://localhost:8080/c/project2/+/67890
    还有这个：http://localhost:8080/c/project3/+/11111
    )";
    clipboard->setText(multipleUrls);

    QRegularExpressionMatchIterator iterator = changeUrlRegex.globalMatch(multipleUrls);
    QStringList extractedIds;
    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();
        extractedIds.append(match.captured(1));
    }

    QCOMPARE(extractedIds.size(), 3);
    QVERIFY(extractedIds.contains("12345"));
    QVERIFY(extractedIds.contains("67890"));
    QVERIFY(extractedIds.contains("11111"));

    qDebug() << "Extracted change IDs:" << extractedIds;
}

void IntegrationTests::testBatchQueryFromClipboard()
{
    if (!loadTestConfig() || !isGerritAvailable()) {
        QSKIP("Test environment not available");
        return;
    }

    // 设置剪贴板内容
    QClipboard* clipboard = QApplication::clipboard();
    QString clipboardContent = R"(
    请查看以下变更：
    http://localhost:8080/c/test-project/+/1
    http://localhost:8080/c/test-project/+/2
    )";
    clipboard->setText(clipboardContent);

    gerritClient_->setConfig(testConfig_);

    // 模拟批量查询（这里简化为单个查询测试）
    auto task = gerritClient_->queryChanges("change:1 OR change:2");
    QString taskId = taskManager_->addTask(task);
    taskManager_->startTask(taskId);

    waitForTask(taskId);

    // 验证任务完成（即使没有找到变更也应该正常完成）
    auto taskState = taskManager_->getTaskState(taskId);
    QVERIFY(taskState == testd::Task::State::FINISHED || taskState == testd::Task::State::ERROR);
}

void IntegrationTests::testCreateTestCommit()
{
    // 创建测试仓库和提交
    QDir::root().mkpath(testRepoPath_);
    QDir testDir(testRepoPath_);

    // 初始化git仓库
    QProcess gitInit;
    gitInit.setWorkingDirectory(testRepoPath_);
    gitInit.start("git", QStringList() << "init");
    gitInit.waitForFinished();

    if (gitInit.exitCode() != 0) {
        QSKIP("Git not available");
        return;
    }

    // 配置git用户
    QProcess gitConfig1;
    gitConfig1.setWorkingDirectory(testRepoPath_);
    gitConfig1.start("git", QStringList() << "config" << "user.name" << "Test User");
    gitConfig1.waitForFinished();

    QProcess gitConfig2;
    gitConfig2.setWorkingDirectory(testRepoPath_);
    gitConfig2.start("git", QStringList() << "config" << "user.email" << "<EMAIL>");
    gitConfig2.waitForFinished();

    // 创建测试文件
    QString testFilePath = testDir.filePath("test.txt");
    QFile testFile(testFilePath);
    QVERIFY(testFile.open(QIODevice::WriteOnly));
    testFile.write("This is a test file for Gerrit integration testing.\n");
    testFile.close();

    // 添加文件到git
    QProcess gitAdd;
    gitAdd.setWorkingDirectory(testRepoPath_);
    gitAdd.start("git", QStringList() << "add" << "test.txt");
    gitAdd.waitForFinished();
    QCOMPARE(gitAdd.exitCode(), 0);

    // 创建提交
    QProcess gitCommit;
    gitCommit.setWorkingDirectory(testRepoPath_);
    gitCommit.start("git", QStringList() << "commit" << "-m" << "Test commit for Gerrit integration");
    gitCommit.waitForFinished();
    QCOMPARE(gitCommit.exitCode(), 0);

    // 获取提交哈希
    QProcess gitRev;
    gitRev.setWorkingDirectory(testRepoPath_);
    gitRev.start("git", QStringList() << "rev-parse" << "HEAD");
    gitRev.waitForFinished();
    QString commitHash = gitRev.readAllStandardOutput().trimmed();

    QVERIFY(!commitHash.isEmpty());
    qDebug() << "Created test commit:" << commitHash;
}

void IntegrationTests::testPushAndQuery()
{
    if (!loadTestConfig() || !isGerritAvailable()) {
        QSKIP("Test environment not available");
        return;
    }

    if (testRepository_.isEmpty()) {
        QSKIP("Test repository not configured");
        return;
    }

    qDebug() << "Testing with repository:" << testRepository_;

    // 测试查询指定项目的变更
    gerritClient_->setConfig(testConfig_);
    QString query = QString("project:%1 status:open limit:5").arg(testRepository_);
    auto task = gerritClient_->queryChanges(query);
    QString taskId = taskManager_->addTask(task);

    QSignalSpy changesSignal(gerritClient_.get(), &GerritClient::changesReceived);
    taskManager_->startTask(taskId);

    waitForTask(taskId);

    auto taskState = taskManager_->getTaskState(taskId);
    if (taskState == testd::Task::State::FINISHED) {
        qDebug() << "Project query completed successfully";

        if (changesSignal.count() > 0) {
            auto changes = changesSignal.at(0).at(0).value<QList<ChangeInfo>>();
            qDebug() << "Found" << changes.size() << "changes in project" << testRepository_;

            // 验证查询结果
            for (const auto& change : changes) {
                QCOMPARE(change.project, testRepository_);
                QVERIFY(!change.id.isEmpty());
                QVERIFY(change.number > 0);
                qDebug() << "  Change" << change.number << ":" << change.subject;
            }
        } else {
            qDebug() << "No changes found in project" << testRepository_;
        }
    } else {
        qDebug() << "Project query failed or timed out";
    }
}

void IntegrationTests::testInfoDataValidation()
{
    // 测试Info和Data数据验证
    ChangeInfo change;
    change.id = "test-change-id";
    change.number = 12345;
    change.subject = "Test change subject";
    change.status = "NEW";
    change.owner = "Test Owner";
    change.project = "test-project";
    change.branch = "main";
    change.created = QDateTime::currentDateTime();
    change.updated = QDateTime::currentDateTime();

    // 验证必要字段
    QVERIFY(!change.id.isEmpty());
    QVERIFY(change.number > 0);
    QVERIFY(!change.subject.isEmpty());
    QVERIFY(!change.status.isEmpty());
    QVERIFY(!change.owner.isEmpty());
    QVERIFY(!change.project.isEmpty());
    QVERIFY(!change.branch.isEmpty());

    // 测试JSON序列化
    QJsonObject json = change.toJson();
    QCOMPARE(json["id"].toString(), change.id);
    QCOMPARE(json["_number"].toInt(), change.number);
    QCOMPARE(json["subject"].toString(), change.subject);
    QCOMPARE(json["status"].toString(), change.status);
    QCOMPARE(json["project"].toString(), change.project);
    QCOMPARE(json["branch"].toString(), change.branch);

    // 验证包含关键字段
    QVERIFY(json.contains("id"));
    QVERIFY(json.contains("_number"));
    QVERIFY(json.contains("subject"));
    QVERIFY(json.contains("status"));
    QVERIFY(json.contains("owner"));
    QVERIFY(json.contains("project"));
    QVERIFY(json.contains("branch"));

    // 测试反序列化
    ChangeInfo restored = ChangeInfo::fromJson(json);
    QCOMPARE(restored.id, change.id);
    QCOMPARE(restored.number, change.number);
    QCOMPARE(restored.subject, change.subject);
    QCOMPARE(restored.status, change.status);
    QCOMPARE(restored.project, change.project);
    QCOMPARE(restored.branch, change.branch);

    qDebug() << "ChangeInfo validation passed";
}

void IntegrationTests::testMainWindowIntegration()
{
    // 测试主窗口集成
    mainWindow_ = std::make_unique<MainWindow>();

    QVERIFY(mainWindow_ != nullptr);

    // 测试窗口创建成功
    qDebug() << "MainWindow created successfully";

    // 注意：在测试环境中不显示窗口，避免GUI依赖
    // mainWindow_->show();
    // QVERIFY(mainWindow_->isVisible());

    qDebug() << "MainWindow integration test completed";
}

void IntegrationTests::testCommandParsing()
{
    // 测试命令解析功能
    struct CommandTest {
        QString input;
        QString expectedCommand;
        bool shouldBeValid;
    };

    QList<CommandTest> tests = {
        {"help", "help", true},
        {"my", "my", true},
        {"drafts", "drafts", true},
        {"reviews", "reviews", true},
        {"ai", "ai", true},
        {"review +1", "review", true},
        {"review -1", "review", true},
        {"batch", "batch", true},
        {"invalid_command", "invalid_command", false},
        {"", "", false}
    };

    for (const auto& test : tests) {
        QString command = test.input.split(' ').first();

        if (test.shouldBeValid) {
            QCOMPARE(command, test.expectedCommand);
        } else {
            // 对于无效命令，验证它们不在有效命令列表中
            QStringList validCommands = {"help", "my", "drafts", "reviews", "ai", "review", "batch"};
            QVERIFY(!validCommands.contains(command) || command.isEmpty());
        }
    }

    qDebug() << "Command parsing tests completed";
}

void IntegrationTests::testNetworkErrorHandling()
{
    // 测试网络错误处理
    gerritClient_ = std::make_unique<GerritClient>();

    // 设置无效的服务器配置
    GerritConfig invalidConfig;
    invalidConfig.serverUrl = "http://invalid-server-that-does-not-exist.com";
    invalidConfig.username = "testuser";
    invalidConfig.password = "testpass";

    gerritClient_->setConfig(invalidConfig);

    // 测试连接失败处理
    auto task = gerritClient_->testConnection();
    QString taskId = taskManager_->addTask(task);

    QSignalSpy errorSignal(gerritClient_.get(), &GerritClient::errorOccurred);
    taskManager_->startTask(taskId);

    waitForTask(taskId, 5000); // 5秒超时

    auto taskState = taskManager_->getTaskState(taskId);
    // 网络错误应该导致任务失败
    QVERIFY(taskState == testd::Task::State::ERROR || taskState == testd::Task::State::FINISHED);

    qDebug() << "Network error handling test completed";
}

void IntegrationTests::testRepositoryConfiguration()
{
    // 测试repository配置
    if (!loadTestConfig()) {
        QSKIP("No test config available");
        return;
    }

    // 验证repository配置已加载
    QVERIFY(!testRepository_.isEmpty());
    qDebug() << "Test repository configured:" << testRepository_;

    // 测试repository相关的查询构建
    QString projectQuery = QString("project:%1").arg(testRepository_);
    QVERIFY(projectQuery.contains(testRepository_));

    // 如果Gerrit可用，测试项目特定查询
    if (isGerritAvailable()) {
        gerritClient_->setConfig(testConfig_);

        QString query = QString("project:%1 limit:1").arg(testRepository_);
        auto task = gerritClient_->queryChanges(query);
        QString taskId = taskManager_->addTask(task);
        taskManager_->startTask(taskId);

        waitForTask(taskId, 8000); // 8秒超时

        auto taskState = taskManager_->getTaskState(taskId);
        if (taskState == testd::Task::State::FINISHED) {
            qDebug() << "Repository-specific query completed successfully";
        } else {
            qDebug() << "Repository-specific query failed or timed out";
        }
    }

    qDebug() << "Repository configuration test completed";
}

// 辅助方法实现
bool IntegrationTests::loadTestConfig()
{
    if (!QFile::exists(testConfigPath_)) {
        return false;
    }

    QSettings settings(testConfigPath_, QSettings::IniFormat);
    settings.beginGroup("Gerrit");

    testConfig_.serverUrl = settings.value("serverUrl").toString();
    testConfig_.username = settings.value("username").toString();
    testConfig_.password = settings.value("password").toString();
    testRepository_ = settings.value("repository").toString();

    settings.endGroup();

    return !testConfig_.serverUrl.isEmpty() && !testConfig_.username.isEmpty();
}

void IntegrationTests::createTestConfigFile()
{
    QSettings settings(testConfigPath_, QSettings::IniFormat);
    settings.beginGroup("Gerrit");
    settings.setValue("serverUrl", "http://localhost:8080");
    settings.setValue("username", "admin");
    settings.setValue("password", "");
    settings.setValue("repository", "test");
    settings.endGroup();

    settings.beginGroup("TestRepo");
    settings.setValue("path", "/path/to/test/repo");
    settings.setValue("remote", "origin");
    settings.setValue("branch", "main");
    settings.endGroup();

    settings.sync();

    qDebug() << "Created test config file:" << testConfigPath_;
}

bool IntegrationTests::isGerritAvailable()
{
    if (testConfig_.serverUrl.isEmpty()) {
        return false;
    }

    QNetworkAccessManager manager;
    QNetworkRequest request(QUrl(testConfig_.serverUrl));
    request.setRawHeader("User-Agent", "Gerrit-Client-Qt-Test");

    QNetworkReply* reply = manager.get(request);
    QEventLoop loop;
    connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);

    QTimer::singleShot(5000, &loop, &QEventLoop::quit); // 5秒超时
    loop.exec();

    bool available = (reply->error() == QNetworkReply::NoError);
    reply->deleteLater();

    return available;
}

QString IntegrationTests::createTestCommit()
{
    // 这个方法在testCreateTestCommit中已经实现
    // 这里返回一个模拟的commit hash
    return "abc123def456";
}

void IntegrationTests::waitForTask(const QString& taskId, int timeoutMs)
{
    QElapsedTimer timer;
    timer.start();

    while (timer.elapsed() < timeoutMs) {
        auto state = taskManager_->getTaskState(taskId);
        if (state == testd::Task::State::FINISHED || state == testd::Task::State::ERROR) {
            return;
        }

        QCoreApplication::processEvents();
        QThread::msleep(100);
    }

    qWarning() << "Task" << taskId << "timed out after" << timeoutMs << "ms";
}

void IntegrationTests::printConfigHelp()
{
    qDebug() << "";
    qDebug() << "=== Configuration Help ===";
    qDebug() << "To run integration tests, create a config file at:" << testConfigPath_;
    qDebug() << "";
    qDebug() << "Example content:";
    qDebug() << "[Gerrit]";
    qDebug() << "serverUrl=http://localhost:8080";
    qDebug() << "username=admin";
    qDebug() << "password=your_password";
    qDebug() << "repository=your_test_repository";
    qDebug() << "";
    qDebug() << "[TestRepo]";
    qDebug() << "path=/path/to/test/repo";
    qDebug() << "remote=origin";
    qDebug() << "branch=main";
    qDebug() << "";
    qDebug() << "Make sure your Gerrit server is running and accessible.";
    qDebug() << "For local testing, you can use the manage-gerrit.sh script:";
    qDebug() << "  cd tests && ./manage-gerrit.sh start";
    qDebug() << "";
}

#include "integration_tests.moc"

// 测试主函数
QTEST_MAIN(IntegrationTests)
